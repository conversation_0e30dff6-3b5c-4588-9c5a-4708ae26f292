<template>
  <div class="pages">
    <div class="box">
      <div class="logo">
        <div class="logo_svg" @click="switchEntranceClick">
          <img src="../../assets/logo.svg" style="width: 50px; height: 50px;">
        </div>
      </div>
      <div v-if="this.$store.state.releaseType === 'test'" style="text-align:center;width:100%;color:#0d0">测试模式<div>3.4.2</div>
      </div>
      <div v-if="mode=='login'" class="box_c">
        <van-form class="van-form" @submit="onSubmit">
          <van-field v-model="mobile" name="mobile" placeholder="手机号" :rules="[{ required: true, pattern, message: '请输入正确的手机号' }]" @input="testTel" />
          <van-field readonly clickable v-model="companyName" name="companyName" placeholder="请选择公司" @click="showPicker = true" />

          <van-field v-model="password" type="password" name="password" placeholder="密码" ref="passautofocus" :rules="[{ required: true, validator, message: '至少输入六位以上的密码，且不能包含空格' }]" />

          <div class="login_submit">
            <div class="row">
              <van-button :type='this.buttonType' round block native-type="submit">
                登录
              </van-button>
            </div>
            <div class="row">
              <van-checkbox class="privacy-container" v-model="privacyChecked">
                <div class="privacy" @click="showPrivacy=true;">同意<span>《APP隐私政策》</span>、</div>
               </van-checkbox>
            </div>

            <div class="row">
              <a class="register" @click="mode='forget'">
                忘记密码
              </a>
              <a v-if="isiOS" class="register" @click="gotoRegister">
                注册新账号
              </a>
            </div>

          </div>
        </van-form>
      </div>
      <div v-if="mode=='forget'" class="box_c">
        <van-form class="van-form" @submit="onForgetSubmit">
          <van-field v-model="mobile" name="mobile" placeholder="手机号" :rules="[{ required: true, pattern, message: '请输入正确的手机号' }]" @input="testTel" />
          <van-field readonly clickable v-model="companyName" name="companyName" placeholder="请选择公司" @click="showPicker = true" />
          <van-field v-model="verifyCode" center clearable placeholder="请输入短信验证码">

            <template #button>
              <van-button class="sendSmsBtn" @click="sendSms" :disabled='sendSmsDisabled' size="mini" type="primary">
                <span style="font-size:2px;" v-if="sendSmsDisabled">剩余{{remainSeconds}}秒可重发</span>
                <span style="font-size:2px;" v-else>发送验证码</span>
              </van-button>
            </template>
          </van-field>
          <van-field v-model="password" type="password" name="password" placeholder="密码" ref="passautofocus" :rules="[{ required: true, validator, message: '至少输入六位以上的密码，且不能包含空格' }]" />
          <div class="login_submit">
            <van-button :type='this.buttonType' round block native-type="submit">
              修改密码
            </van-button>

          </div>
        </van-form>
      </div>
      <van-popup v-model="policyShow">{{policy}}</van-popup>
      <van-popup v-model="showPicker" round position="bottom">
        <van-picker show-toolbar :columns="companyList" value-key="company_name" @cancel="showPicker = false" @confirm="onConfirm" />
      </van-popup>
      <van-popup v-model="showPrivacy" closable :style="{ height: '60%' }" round block position="bottom">
        <div v-html="privacy"></div>
      </van-popup>
      <van-popup v-model="showUserAgreement" closable :style="{ height: '60%' }" round block position="bottom">
        <div v-html="userAgreement"></div>
      </van-popup>
    </div>
    <div class="privacy-wrapper" v-show="showPrivacy || showUserAgreement">
      <button class="my-btn" style="width:120px;" @click="rejectPrivacy">拒绝</button>
      <button class="my-btn main-btn" style="width:120px;" @click="acceptPrivacy">同意</button>
    </div>
  </div>
</template>
<script>

import policy from '../Login/policy.js'
import { ForgetResetPwd, SendResetPwdSms, GetUserCompanies, Login, SetApiServer, GetInfoRegionName } from '../../api/api'
import { userAgreement, privacy } from './privacy.js'
import { Checkbox, Form, Field, Button, Popup, Picker, Toast } from 'vant'

import { API_SERVER_STATUS } from '../../../apiserverConfig'
export default {
  name: "Login",
  data() {
    return {
      buttonType: "info",
      switchEntranceClickCount: 0,
      mobile: '',
      password: '',
      companyId: '',
      companyName: '',
      companyNameSetting: '',
      verifyCode: '',
      pattern: /1\d{10}/,
      verifyCodePattern: /\d{4}/,
      sendSmsDisabled: false,
      remainSeconds: 60,
      passpattern: /\w{6,}/,
      showPicker: false,
      companyList: [],
      timerHandle: 0,
      mode: 'login',
      policy: "",
      policyShow: false,
      acceptPolicy: false,
      showPrivacy: false,
      showUserAgreement: false,
      privacyChecked: false,
      privacy: '',
      userAgreement: ''
    }
  },
  computed: {
    isiOS() {
      return window.isiOS
    }
  },
  watch: {
    /*    showPrivacy(newVal,oldVal){
          if(newVal) {
            this.privacyChecked = false
            this.$store.commit("acceptPrivacy",false)
          }

        },
        showUserAgreement(newVal,oldVal) {
          if(newVal) {
            this.privacyChecked = false
            this.$store.commit("acceptPrivacy",false)
          }
        }*/
  },
  mounted() {
    setGlobalFontSize(1) 
    var that_ = this
    //this.privacy = privacy
    //this.userAgreement = userAgreement
    this.userAgreement = userAgreement.replace(/OEM_COMPANY_NAME/g, this.getOemCompanyName());
   
    this.privacy = privacy.replace(/OEM_COMPANY_NAME/g, this.getOemCompanyName());
    
    //this.privacyChecked=this.$store.state.acceptPrivacy
    this.changeLoginBtnStyle()

  },
  components: {
    "van-form": Form,
    "van-field": Field,
    "van-button": Button,
    "van-popup": Popup,
    "van-picker": Picker,
    "van-checkbox": Checkbox
  },
  methods: {
    acceptPrivacy() {
      this.privacyChecked = true;
      this.showPrivacy = false;
      this.showUserAgreement = false
      this.$store.commit("acceptPrivacy", true)
    },
    rejectPrivacy() {
      this.showPrivacy = false;
      this.privacyChecked = false
      this.showUserAgreement = false
      this.$store.commit("acceptPrivacy", false)
    },
    sendSms() {
      const param = {
        mobile: this.mobile
      }
      SendResetPwdSms(param).then(res => {
        this.sendSmsDisabled = true
        const remainSecondsInterval = setInterval(() => {
          this.remainSeconds--
          console.log(this.remainSeconds)
        }, 1000)
        setTimeout(() => {
          this.sendSmsDisabled = false
          this.remainSeconds = 60
          window.clearInterval(remainSecondsInterval)
        }, 60000)
      })
    },
    switchEntranceClick() {
      if (this.switchEntranceClickCount == 0) {
        var that = this
        this.timerHandle = setTimeout(() => {
          that.switchEntranceClickCount = 0
        }, 8000)
      }

      if (this.switchEntranceClickCount < 7) {
        this.switchEntranceClickCount++
        return
      }

      clearTimeout(this.timerHandle)

      this.switchServer()
      this.changeLoginBtnStyle()
      this.switchEntranceClickCount = 0
    },

    switchServer() {
      //假如当前缓存的环境状态是线上版本，那么经过连续十次点击切换到测试版 反之，则测试切换回线上
      if (this.$store.state.releaseType == API_SERVER_STATUS.PRODUCTION) {
        Toast('进入测试模式')
        this.switchReleaseType(API_SERVER_STATUS.TEST)
      }
      else {
        Toast('进入发行模式')
        this.switchReleaseType(API_SERVER_STATUS.PRODUCTION)
      }
    },
    changeLoginBtnStyle() {
      if (this.$store.state.releaseType == API_SERVER_STATUS.PRODUCTION) {
        this.buttonType = "info"
      } else {
        this.buttonType = "primary"
      }
    },
    switchReleaseType(toReleaseType) {
      switch (toReleaseType) {
        case API_SERVER_STATUS.TEST:
          console.log("switchreleasetype API_SERVER_STATUS.TEST")
          SetApiServer(process.env.VUE_APP_API_TEST_URL)
          this.$store.commit('releaseType', API_SERVER_STATUS.TEST)
          break;
        case API_SERVER_STATUS.PRODUCTION:
          console.log("switchreleasetype API_SERVER_STATUS.PRODUCTION")
          SetApiServer(process.env.VUE_APP_API_URL)
          this.$store.commit('releaseType', API_SERVER_STATUS.PRODUCTION)
      }
      fetchCode(this.$store.state.releaseType)
    },
    testTel() {
      let params = {
        mobile: this.mobile
      }
      if (this.mobile == '4949') {
        window.fetchCode(this.$store.state.releaseType)
        this.$toast('正在检测新版本')
        return
      }
      let that = this
      if (that.pattern.test(params.mobile)) {
        GetUserCompanies(params).then(res => {
          if (res.result === 'OK') {
            let listData = res.data.map(item => {
              return JSON.parse(item) //JSON字符串转对象
            })
            that.companyList = listData
            that.companyId = that.companyList[0].company_id
            that.companyName = that.companyList[0].company_name
            that.companyNameSetting = that.companyList[0].company_name_setting
            that.$refs.passautofocus.focus()
          }
          else {
            Toast(res.msg)
          }
        })
      }
    },
    onConfirm(value) {
      this.companyId = value.company_id
      this.companyName = value.company_name
      this.companyNameSetting = value.company_name_setting
      this.showPicker = false
    },
    validator(val) {
      if (val.trim().length >= 6) return true
      else return false
      //return /\w{6,}/.test(val)
    },
    onForgetSubmit() {
      if (this.mobile === '' || this.verifyCode === '' || this.password === '') {
        this.$toast("请检查必填项")
        return
      }
      let params = {
        mobile: this.mobile,
        companyId: this.companyId,
        passwordNew: this.password,
        verifyCode: this.verifyCode
      }
      ForgetResetPwd(params).then(res => {
        Toast(res.msg)
        if (res.operKey) this.$store.commit('operKey', res.operKey)
      })
    },
    getInfoRegionName(operKey) {
      GetInfoRegionName({ operKey }).then(res => {
        this.$store.commit('infoRegionObj', res.data)
      }).catch(err => {
        console.log(err)
      })
    },
    onSubmit() {
      if (!this.privacyChecked) {
        this.$toast("请您先同意下方的《APP隐私政策》！")
        return
      }
      let params = {
        mobile: this.mobile,
        companyId: this.companyId,
        password: this.password
      }
      if (typeof window.push != 'undefined') {
        params.regID = window.push.regId
        params.platform = window.push.manufactor
      }
      Login(params).then(res => {
        if (res.result === 'OK') {
          console.log(res)
          //验证通过后存入本地缓存
          let operKey = res.operKey
          let account = {
            mobile: this.mobile,
            companyId: this.companyId,
            companyName: this.companyName,
            companyNameSetting: this.companyNameSetting,
            password: this.password,
            isActivated: true
            
          }
          let companyList = this.companyList
          let server_uri = res.data.server_uri
          this.$store.commit('server_uri', server_uri.startsWith("http") ? server_uri : "http://" + server_uri)
          this.$store.commit('operKey', operKey)
          this.$store.commit('account', account)
          this.handleAccountToList(account)
          this.$store.commit('companyList', companyList)
          this.$router.replace({ path: '/Workbench',query:{fromLogin:true}})
          this.getInfoRegionName(operKey);
          //getOperRights(this.$store.state.operKey, function (res) {
          //})
          //window.fetchCode(this.$store.state.releaseType)
        } else {
          Toast(res.msg)
        }
      })
    },
    handleAccountToList(account) {
      let accountList = this.$store.state.accountList
      if (accountList.length == 0) {
        accountList.unshift(account)
      } else {
        accountList.forEach(item => { //统一置为未激活状态
          item.isActivated = false
        });
        let replaceFlag = false
        for (let i = 0, length = accountList.length; i < length; i++) {
          if (accountList[i].mobile === account.mobile && accountList[i].companyId === account.companyId) { // 存在唯一账号进行替换
            accountList.splice(i, 1, account)
            replaceFlag = true
            break
          }
        }
        if (!replaceFlag) { // 没有找到替换进行push
          accountList.unshift(account)
        }
      }
      this.$store.commit('accountList', accountList)
    },
    gotoRegister() {
      this.$router.push({ path: '/Register' })
    }
  }
}
</script>
<style lang="less" scoped>
input::-ms-input-placeholder {
  text-align: center;
}
input::-webkit-input-placeholder {
  text-align: center;
}

/deep/.van-field__control,
/deep/.van-field__error-message {
  text-align: center;
}
/deep/.van-button--info {
  background-color: #c61848 !important;
  border: 1px solid #c61848 !important;
}
/deep/.van-cell {
  font-size: 16px !important;
  background-color: rgba(255, 255, 255, 0.5) !important;
}
/deep/.van-cell:nth-child(1) {
  border-radius: 15px 15px 0 0;
}
/deep/.van-cell:nth-child(3) {
  border-radius: 0 0 15px 15px;
}

/deep/.van-field {
  background-color: #fff;
}
/deep/.van-button--normal {
  font-size: 16px;
}
.privacy-container {
  height: 80px;
  display: flex;
  @media (min-width:500px) {
    height:40px;
  }
}
.privacy {
  color: #188fc6;
  border-bottom: 1px solid;
}
.van-field__label {
  width: 60px !important;
}
.van-cell__value {
  background-color: #eee;
}
.policy {
  border-bottom: 1px solid rgb(0, 154, 243);
  width: 80px;
  color: rgb(0, 154, 243);
}
.box {
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  .logo {
    width: 100%;
    margin-top: 20px;
    .logo_svg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 30px;
    }
  }
  .box_c {
    height: calc(100% - 110px);
    position: relative;
    padding: 0 40px;
    .van-form {
      position:absolute;
      width: calc(100% - 80px);
      bottom: 10%;
      .login_submit {
        margin: 35px 16px 0;
        display: flex !important;
        flex-direction: column !important;
        align-items: center;
      }

      .login_submit > .row {
        display: flex;
        flex-direction: row;
        justify-content: center;
        margin-top: 14px;
        width: 100%;
      }
      .register {
        font-size: 16px;
        color: #666;
        border-bottom: currentColor 1px solid;
      }
      .sendSmsBtn {
        font-size: 16px;
      }
    }
  }
}
.privacy-wrapper {
  position: absolute;
  bottom: 20px;
  z-index: 100000;
  width: 100%;
  display: flex;
  justify-content: space-around;
  font-size: 16px;
  box-sizing: border-box;
  .btn-wrapper {
    flex: 1;
    background-color: #61d47b;
    border-radius: 5px;
    margin: 0 10px;
  }
  .btn-wrapper-reject {
    flex: 1;
    background-color: #fde3e4;
    border-radius: 5px;
    margin: 0 10px;
  }
}
.privacy {
  border-bottom: none;
  padding: 5px 0;
  @media (min-width: 500px){
    display: inline;
  }
  span {
    border-bottom: 1px solid #c61848;
    color: #c61848;
  }
}
</style>
