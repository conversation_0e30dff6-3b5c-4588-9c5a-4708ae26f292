<template>
  <div class="pages">
    <div class="box">
      <div class="logo">
        <div class="logo_svg">
          <img src="../../assets/logo.svg"  style="width: 50px; height: 50px;">
        </div>
      </div>
      <div class="box_c">
        <van-form class="van-form" @submit="register">
          <van-field
            v-model="userPhone"
            name="mobile"
            label="手机号"
            aria-placeholder="11位手机号"
            :rules="[{ required: true, pattern, message: '请输入11位手机号' }]"
            @input="testTel"
          />
          <van-field
            v-model="userName"
            name="userName"
            label="姓名"
            :rules="[{ required: true, message: '请输入用户名' }]"
          />
           <van-field
            v-model="password"
            type="password"
            name="password"
            label="密码"
            :rules="[{ required: true, message: '请输入密码' }]"
          />

          <div class="login_submit">
            <van-button :type="this.buttonType" round block  native-type="submit">
              注册
            </van-button>
            <a class="return" href="" @click="goback">
                返回登陆
            </a>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>
<script>
import { Form, Field, Button, Toast} from 'vant'
import { RegisterApi, GetUserCompanies } from '../../api/api'
export default {
    name: 'Register',
    data() {
        return {
            pattern: /1\d{10}/,
            buttonType: 'info',
            userPhone: '',
            userName: '',
            password:''
        }
    },
    components:{
        "van-form": Form,
        "van-field": Field,
        "van-button": Button,
    },
    methods:{
        goback(){
            myGoBack(this.$router)
        },

        testTel() {
            let params = {
                mobile: this.userPhone,
            }
            var that = this
            if(this.pattern.test(params.mobile)){
                GetUserCompanies(params).then(res => {
                    if(res.result === 'OK'){
                        Toast.fail("该手机号已注册过")
                        that.userPhone = ''
                    }
                })
            }
        },
        register(){
            if( !this.userPhone ){
                Toast.fail("手机号不能为空")
                return
            }
            if( !this.userName ){
                Toast.fail("用户名不能为空")
                return
            }
            let params = {
                mobile: this.userPhone,
                userName: this.userName,
                password:this.password
            }
            console.log(params)
            RegisterApi(params).then(res => {
                console.log(res)
                if(res.result=='OK'){
                    Toast.success("注册成功！\n请返回登陆")
                }
                else{
                    Toast.fail(res.msg)
                }
            })
        }
    }

}
</script>
<style lang="less" scoped>
/deep/.van-button--info{
  background-color: #c61848!important;
  border: 1px solid #c61848!important;
}
/deep/.van-field{
  background-color: #fff;
}
.van-field__label{
  width: 60px!important;
}
.van-cell__value{
  background-color: #eee;
}
.box{
  height: 100vh; /* 使用视口高度 */
  background: rgba(255,255,255,.1);
  position: relative;
  overflow: hidden; /* 防止滚动 */
  .logo{
      width: 100%;
      margin-top: 20px;
      .logo_svg{
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 30px;
      }
    }
  .box_c{
    height: calc(100% - 110px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 40px;
    .van-form{
      width: 100%;
      margin-top: -50px; /* 向上调整表单位置 */
      .login_submit{
        margin: 35px 16px 0;

        .return{
        font-size: 16px;
        border-bottom: currentColor 1px solid;
        }
      }
    }
  }
}

/* 软键盘弹出时的适配 */
@media (max-height: 600px) {
  .box {
    .logo {
      margin-top: 10px;
    }
    .box_c {
      .van-form {
        margin-top: -80px;
      }
    }
  }
}

@media (max-height: 500px) {
  .box {
    .logo {
      margin-top: 5px;
      .logo_svg {
        padding: 5px 30px;
      }
    }
    .box_c {
      .van-form {
        margin-top: -100px;
        .login_submit {
          margin: 20px 16px 0;
        }
      }
    }
  }
}
</style>