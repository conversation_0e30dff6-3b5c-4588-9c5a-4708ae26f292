<template>
  <div class="route-planning-container">
    <!-- 头部 -->
    <div class="route-header">
      <h3>送货路线规划</h3>
      <p class="route-subtitle">基于百度地图智能规划最优送货路线</p>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <div id="routeMap" class="route-map"></div>

      <!-- 地图控制按钮 -->
      <div class="map-controls">
        <van-button
          size="small"
          type="primary"
          @click="resetMap"
          icon="replay"
        >
          重置
        </van-button>
        <van-button
          size="small"
          type="info"
          @click="optimizeRoute"
          icon="guide-o"
          :loading="optimizing"
        >
          智能优化
        </van-button>
      </div>
    </div>

    <!-- 订单列表 -->
    <div class="order-list-container">
      <div class="list-header">
        <span>送货订单 ({{ validOrders.length }})</span>
        <span class="total-distance" v-if="totalDistance">
          总距离: {{ totalDistance }}
        </span>
      </div>

      <div class="order-list">
        <div
          v-for="(order, index) in sortedOrders"
          :key="order.order_sheet_id"
          class="order-item"
          :class="{ 'active': selectedOrderIndex === index }"
          @click="selectOrder(index)"
        >
          <div class="order-number">{{ index + 1 }}</div>
          <div class="order-info">
            <div class="customer-name">{{ order.sup_name }}</div>
            <div class="order-details">
              <span class="order-no">{{ order.order_sheet_no }}</span>
              <span class="order-amount">¥{{ order.total_amount }}</span>
            </div>
            <div class="customer-address">{{ order.sup_addr }}</div>
          </div>
          <div class="order-actions">
            <van-icon
              name="arrow-up"
              @click.stop="moveOrder(index, -1)"
              :class="{ disabled: index === 0 }"
            />
            <van-icon
              name="arrow-down"
              @click.stop="moveOrder(index, 1)"
              :class="{ disabled: index === sortedOrders.length - 1 }"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="route-footer">
      <van-button
        size="large"
        @click="$emit('onClose')"
        style="margin-right: 10px;"
      >
        取消
      </van-button>
      <van-button
        size="large"
        type="primary"
        @click="confirmRoute"
        :loading="saving"
      >
        确定路线
      </van-button>
    </div>
  </div>
</template>

<script>
import { Button, Icon, Toast } from 'vant';

export default {
  name: 'DeliveryRoutePlanning',
  components: {
    'van-button': Button,
    'van-icon': Icon
  },
  props: {
    orderList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      map: null,
      markers: [],
      polyline: null,
      sortedOrders: [],
      selectedOrderIndex: -1,
      optimizing: false,
      saving: false,
      totalDistance: '',
      currentPosition: null
    };
  },
  computed: {
    validOrders() {
      return this.orderList.filter(order =>
        order.addr_lng && order.addr_lat &&
        order.addr_lng !== '0' && order.addr_lat !== '0'
      );
    }
  },
  async mounted() {
    await this.initBaiduMap();
    this.initOrderList();
    this.getCurrentPosition();
  },
  beforeDestroy() {
    if (this.map) {
      this.map.clearOverlays();
    }
  },
  methods: {
    // 初始化百度地图
    async initBaiduMap() {
      try {
        // 确保百度地图API已加载
        if (!window.BMap) {
          await this.loadBaiduMapScript();
        }

        this.map = new BMap.Map('routeMap');
        this.map.centerAndZoom(new BMap.Point(116.404, 39.915), 12);
        this.map.enableScrollWheelZoom(true);

        // 添加地图控件
        this.map.addControl(new BMap.NavigationControl());
        this.map.addControl(new BMap.ScaleControl());

      } catch (error) {
        console.error('初始化百度地图失败:', error);
        Toast.fail('地图加载失败');
      }
    },

    // 加载百度地图脚本
    loadBaiduMapScript() {
      return new Promise((resolve, reject) => {
        if (window.BMap) {
          resolve();
          return;
        }

        // 使用已有的百度地图API密钥
        const script = document.createElement('script');
        script.src = 'https://api.map.baidu.com/api?v=3.0&ak=zbQcVkpGET83cqhFyGIrmqrmkJMiAY4F&callback=initBaiduMapForRoute';
        script.onerror = reject;
        document.head.appendChild(script);

        window.initBaiduMapForRoute = () => {
          resolve();
        };
      });
    },

    // 初始化订单列表
    initOrderList() {
      this.sortedOrders = [...this.validOrders];
      this.renderOrdersOnMap();
    },

    // 获取当前位置
    async getCurrentPosition() {
      try {
        const position = await this.getPosition();
        this.currentPosition = new BMap.Point(position.longitude, position.latitude);

        // 添加当前位置标记
        const marker = new BMap.Marker(this.currentPosition);
        marker.setIcon(new BMap.Icon(
          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iOCIgZmlsbD0iIzAwN0FGRiIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI0IiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K',
          new BMap.Size(24, 24)
        ));
        this.map.addOverlay(marker);

      } catch (error) {
        console.error('获取当前位置失败:', error);
      }
    },

    // 获取位置信息
    async getPosition() {
      try {
        // 使用现有的Position组件
        const Position = await import('../../components/Position');
        const params = {
          message: "需要定位权限来获取当前位置",
          key: "routePlanning"
        };
        return await Position.default.getPosition(params);
      } catch (error) {
        // 降级到浏览器原生定位
        return new Promise((resolve, reject) => {
          if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
              position => resolve(position.coords),
              reject,
              { timeout: 10000 }
            );
          } else {
            reject(new Error('浏览器不支持定位'));
          }
        });
      }
    },

    // 在地图上渲染订单
    renderOrdersOnMap() {
      // 清除现有标记和路线
      this.clearMapOverlays();

      const points = [];

      this.sortedOrders.forEach((order, index) => {
        const point = new BMap.Point(parseFloat(order.addr_lng), parseFloat(order.addr_lat));
        points.push(point);

        // 创建标记
        const marker = new BMap.Marker(point);

        // 自定义标记图标，显示序号
        const label = new BMap.Label(String(index + 1), {
          offset: new BMap.Size(0, -30)
        });
        label.setStyle({
          backgroundColor: '#1989fa',
          color: 'white',
          border: 'none',
          borderRadius: '50%',
          width: '24px',
          height: '24px',
          lineHeight: '24px',
          textAlign: 'center',
          fontSize: '12px',
          fontWeight: 'bold'
        });
        marker.setLabel(label);

        // 添加信息窗口
        const infoWindow = new BMap.InfoWindow(`
          <div style="padding: 10px;">
            <h4>${order.sup_name}</h4>
            <p>订单号: ${order.order_sheet_no}</p>
            <p>金额: ¥${order.total_amount}</p>
            <p>地址: ${order.sup_addr}</p>
          </div>
        `);

        marker.addEventListener('click', () => {
          this.map.openInfoWindow(infoWindow, point);
        });

        this.map.addOverlay(marker);
        this.markers.push(marker);
      });

      // 绘制路线
      if (points.length > 1) {
        this.drawRoute(points);
      }

      // 调整地图视野
      if (points.length > 0) {
        this.map.setViewport(points);
      }
    },

    // 绘制路线
    drawRoute(points) {
      if (this.currentPosition) {
        points.unshift(this.currentPosition);
      }

      const polyline = new BMap.Polyline(points, {
        strokeColor: '#1989fa',
        strokeWeight: 4,
        strokeOpacity: 0.8
      });

      this.map.addOverlay(polyline);
      this.polyline = polyline;

      // 计算总距离
      this.calculateTotalDistance(points);
    },

    // 计算总距离
    calculateTotalDistance(points) {
      let totalDistance = 0;
      for (let i = 1; i < points.length; i++) {
        totalDistance += this.map.getDistance(points[i-1], points[i]);
      }

      if (totalDistance > 1000) {
        this.totalDistance = (totalDistance / 1000).toFixed(2) + ' km';
      } else {
        this.totalDistance = Math.round(totalDistance) + ' m';
      }
    },

    // 清除地图覆盖物
    clearMapOverlays() {
      this.markers.forEach(marker => {
        this.map.removeOverlay(marker);
      });
      this.markers = [];

      if (this.polyline) {
        this.map.removeOverlay(this.polyline);
        this.polyline = null;
      }
    },

    // 选择订单
    selectOrder(index) {
      this.selectedOrderIndex = index;

      // 高亮对应的地图标记
      if (this.markers[index]) {
        const point = new BMap.Point(
          parseFloat(this.sortedOrders[index].addr_lng),
          parseFloat(this.sortedOrders[index].addr_lat)
        );
        this.map.panTo(point);
      }
    },

    // 移动订单位置
    moveOrder(index, direction) {
      const newIndex = index + direction;
      if (newIndex < 0 || newIndex >= this.sortedOrders.length) {
        return;
      }

      // 交换位置
      const temp = this.sortedOrders[index];
      this.$set(this.sortedOrders, index, this.sortedOrders[newIndex]);
      this.$set(this.sortedOrders, newIndex, temp);

      // 重新渲染地图
      this.renderOrdersOnMap();
    },

    // 重置地图
    resetMap() {
      this.sortedOrders = [...this.validOrders];
      this.selectedOrderIndex = -1;
      this.renderOrdersOnMap();
    },

    // 智能优化路线
    async optimizeRoute() {
      if (this.sortedOrders.length < 2) {
        Toast('订单数量不足，无需优化');
        return;
      }

      this.optimizing = true;

      try {
        // 这里可以调用路线优化算法
        // 简单的贪心算法示例
        await this.greedyOptimization();

        Toast.success('路线优化完成');
        this.renderOrdersOnMap();

      } catch (error) {
        console.error('路线优化失败:', error);
        Toast.fail('路线优化失败');
      } finally {
        this.optimizing = false;
      }
    },

    // 贪心算法优化路线
    async greedyOptimization() {
      if (!this.currentPosition) {
        return;
      }

      const unvisited = [...this.sortedOrders];
      const optimized = [];
      let currentPoint = this.currentPosition;

      while (unvisited.length > 0) {
        let nearestIndex = 0;
        let minDistance = Infinity;

        // 找到距离当前位置最近的订单
        unvisited.forEach((order, index) => {
          const orderPoint = new BMap.Point(parseFloat(order.addr_lng), parseFloat(order.addr_lat));
          const distance = this.map.getDistance(currentPoint, orderPoint);

          if (distance < minDistance) {
            minDistance = distance;
            nearestIndex = index;
          }
        });

        // 移动到最近的订单
        const nearestOrder = unvisited.splice(nearestIndex, 1)[0];
        optimized.push(nearestOrder);
        currentPoint = new BMap.Point(parseFloat(nearestOrder.addr_lng), parseFloat(nearestOrder.addr_lat));
      }

      this.sortedOrders = optimized;
    },

    // 确认路线
    confirmRoute() {
      this.saving = true;

      try {
        // 构建路线数据
        const routeData = this.sortedOrders.map((order, index) => ({
          order_sheet_id: order.order_sheet_id,
          route_order: index + 1,
          addr_lng: order.addr_lng,
          addr_lat: order.addr_lat
        }));

        // 发送确认事件
        this.$emit('onRouteConfirm', routeData);

      } catch (error) {
        console.error('确认路线失败:', error);
        Toast.fail('确认路线失败');
      } finally {
        this.saving = false;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.route-planning-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.route-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #eee;
  text-align: center;

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #333;
  }

  .route-subtitle {
    margin: 0;
    font-size: 14px;
    color: #666;
  }
}

.map-container {
  position: relative;
  height: 300px;
  margin: 10px;
  border-radius: 8px;
  overflow: hidden;

  .route-map {
    width: 100%;
    height: 100%;
  }

  .map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 8px;
  }
}

.order-list-container {
  flex: 1;
  margin: 0 10px;
  background: white;
  border-radius: 8px;
  overflow: hidden;

  .list-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;

    .total-distance {
      color: #1989fa;
      font-size: 14px;
    }
  }

  .order-list {
    max-height: 300px;
    overflow-y: auto;
  }
}

.order-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background: #f8f9fa;
  }

  &.active {
    background: #e6f7ff;
    border-left: 3px solid #1989fa;
  }

  .order-number {
    width: 32px;
    height: 32px;
    background: #1989fa;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
  }

  .order-info {
    flex: 1;

    .customer-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .order-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;

      .order-no {
        font-size: 14px;
        color: #666;
      }

      .order-amount {
        font-size: 14px;
        color: #1989fa;
        font-weight: 500;
      }
    }

    .customer-address {
      font-size: 12px;
      color: #999;
      line-height: 1.4;
    }
  }

  .order-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .van-icon {
      font-size: 20px;
      color: #1989fa;
      cursor: pointer;

      &.disabled {
        color: #ccc;
        cursor: not-allowed;
      }
    }
  }
}

.route-footer {
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
  display: flex;
  gap: 10px;

  .van-button {
    flex: 1;
  }
}
</style>
