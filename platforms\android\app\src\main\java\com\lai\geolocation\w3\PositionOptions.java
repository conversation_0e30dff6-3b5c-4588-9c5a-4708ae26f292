package com.lai.geolocation.w3;

import org.json.JSONException;
import org.json.JSONObject;

import android.util.Log;

import com.baidu.location.LocationClientOption;

import static com.baidu.location.LocationClientOption.LocationMode.Hight_Accuracy;

public class PositionOptions {
  interface LOCATION_CLIENT_MODE{
    String OUTGOING="OUTGOING";
    String SIGN="SIGN";
    String SPORT="SPORT";
    String REALTIME="REALTIME"; // 添加：实时定位模式
  }
  private static final String TAG = "PositionOptions";
  //默认SIGN
  private String locationClientMode;
  Boolean enableHighAccuracy = false;
  long maximumAge;
  long timeout;
  String coorType;
  public LocationClientOption createLocationClientOption(String locationClentMode){
    locationClentMode=locationClentMode.toUpperCase();
    LocationClientOption option = new LocationClientOption();
    option.setLocationMode(Hight_Accuracy);
    option.setNeedNewVersionRgc(true);
    option.setIsNeedAddress(true);
    option.setIsNeedAltitude(true);
    option.setOpenGps(true);
    switch (locationClentMode) {
      case LOCATION_CLIENT_MODE.SIGN:
        option.setLocationNotify(false);
        option.setScanSpan(0);
        option.setIsNeedLocationPoiList(true);
        option.setIsNeedLocationDescribe(true);
        option.setWifiCacheTimeOut(1000); // 修改：将WiFi缓存时间从10秒减少到1秒，获得更及时的位置更新
        break;
      case LOCATION_CLIENT_MODE.OUTGOING:
        option.setLocationNotify(true);
        option.setScanSpan(3000);
        option.setIsNeedLocationPoiList(false);
        option.setIsNeedLocationDescribe(false);
        option.setWifiCacheTimeOut(1000);
        break;
      case LOCATION_CLIENT_MODE.SPORT:
        option.setLocationNotify(true);
        option.setScanSpan(1000);
        option.setIsNeedLocationPoiList(false);
        option.setIsNeedLocationDescribe(false);
        option.setWifiCacheTimeOut(1000);
        break;
      case LOCATION_CLIENT_MODE.REALTIME:
        // 实时定位模式：最短缓存时间，强制获取最新位置
        option.setLocationNotify(false);
        option.setScanSpan(0);
        option.setIsNeedLocationPoiList(true);
        option.setIsNeedLocationDescribe(true);
        option.setWifiCacheTimeOut(0); // 不使用WiFi缓存
        option.setIgnoreKillProcess(false);
        option.setIgnoreCacheException(false);
        break;
    }
    return option;
  }
  public PositionOptions(JSONObject options) {
    try {
      this.enableHighAccuracy = options.getBoolean("enableHighAccuracy");
    } catch (JSONException e) {
      Log.v(TAG, "enableHighAccuracy 未定义");
    }
    try {
      this.locationClientMode = options.getString("locationClientMode");
    } catch (JSONException e) {
      Log.v(TAG, "locationClientMode 未定义 默认为签到模式");
      this.locationClientMode=LOCATION_CLIENT_MODE.SIGN;
    }
    try {
      this.coorType = options.getString("coorType");
    } catch (JSONException e) {
      Log.v(TAG, "coorType 未定义");
    }
  }

  public String getLocationClientMode() {
    return locationClientMode;
  }

  public void setLocationClientMode(String locationClientMode) {
    this.locationClientMode = locationClientMode;
  }

  public String getCoorType() {
    return this.coorType;
  }

  public PositionOptions setCoorType(String coorType) {
    this.coorType = coorType;
    return this;
  }

  public Boolean isEnableHighAccuracy() {
    return enableHighAccuracy;
  }

  public PositionOptions setEnableHighAccuracy(Boolean enableHighAccuracy) {
    this.enableHighAccuracy = enableHighAccuracy;
    return this;
  }

  public long getMaximumAge() {
    return maximumAge;
  }

  public PositionOptions setMaximumAge(long maximumAge) {
    this.maximumAge = maximumAge;
    return this;
  }

  public long getTimeout() {
    return timeout;
  }

  public PositionOptions setTimeout(long timeout) {
    this.timeout = timeout;
    return this;
  }

}
