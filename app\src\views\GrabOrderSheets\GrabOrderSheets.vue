<template>
  <div class="pages">
    <van-nav-bar
      title="抢单"
      left-arrow
      @click-left="myGoBack($router)"
      safe-area-inset-top
    >
      <template #right>
        <div style="margin-right:10px;" type="info" @click="btnScanBarcode_click">
          <svg width="24px" height="24px" fill="#555">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </div>
        <div
          class="iconfont icon_right"
          @click="selectShow = !selectShow"
        >
          &#xe690;
        </div>
      </template>
    </van-nav-bar>
    <van-tabs
      v-model="active"
      @change="tabChange"
    >
      <van-tab
        title="未抢单"
        name="0"
        title-active-color="#1989fa"
      >
      </van-tab>
      <van-tab
        title="已抢单"
        name="1"
        title-active-color="#1989fa"
      >
      </van-tab>
      <ViewGrabOrderSheets
        :queryCondiValues="queryCondiValues"
        :queryCondiLabels="queryCondiLabels"
        ref="deliveryReceiptView"
        @handleDateSon="handleDateSon"
      />

    </van-tabs>
    <van-popup
      class="van_popup"
      duration="0.4"
      v-model="selectShow"
      position="right"
      safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }"
    >
      <!-- <h5>
        其它选项
        <van-icon name="cross" @click="selectShow = false" />
      </h5> -->
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <van-cell-group class="cellgroup">
        <van-field
          v-model="queryCondiLabels.dateTimeInfo"
          readonly
          label="日期选择"
          placeholder="日期范围"
          @click="showDate = true"
        />
        <div class="handle_date_btns">
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(0,0)"
            >今天</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-1,-1)"
            >昨天</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-2,0)"
            >3天内</van-button></div>
          <div class="date_btn"><van-button
              plain
              type="default"
              @click="handleDate(-6,0)"
            >7天内</van-button></div>
        </div>
        <van-field
          v-model="queryCondiLabels.sellerName"
          readonly
          label="业务员"
          placeholder="请选择"
          @click="showSeller = true"
          style="border-bottom:2px solid #eee"
        />
        <van-field
          v-model="queryCondiLabels.senderName"
          readonly
          label="送货员"
          placeholder="请选择"
          @click="showSender = true"
          style="border-bottom:2px solid #eee"
        />
        <van-field
          v-model="queryCondiLabels.branchName"
          readonly
          label="订单仓"
          placeholder="请选择"
          @click="showBranch= true"
          style="border-bottom:2px solid #eee"
        />
        <van-field
          v-model="queryCondiLabels.carName"
          readonly
          label="车辆仓"
          placeholder="请选择"
          @click="showCar= true"
          style="border-bottom:2px solid #eee"
        />
      </van-cell-group>
      <div class="footer_button">
        <van-button
          style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc"
          plain
          type="default"
          @click="cancelSelect"
        >清空选择</van-button>
        <van-button
          style="height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc"
          plain
          type="info"
          @click="submitSelect"
        >确认选择</van-button>
      </div>
    </van-popup>
    <van-calendar
      v-model="showDate"
      type="range"
      @confirm="onConfirm"
      title="请选择起止日期"
      :allow-same-day="true"
      :min-date="minDate"
    />
    <van-popup
      v-model="showCustomer"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectCustomer @onClientSelected="selectCustomer" />
    </van-popup>
    <van-popup
      v-model="showSeller"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectSellersWithOperRights
        @selectSellersWithOperRights="selectSellersWithOperRights"
      />
    </van-popup>
    <van-popup
      v-model="showSender"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectSendersWithOperRights
        @selectSendersWithOperRights="selectSendersWithOperRights"
      />
    </van-popup>
    <van-popup
      v-model="showBranch"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectBranchsWithPermission
        :sheetType="'XD'"
        @selectBranchsWithPermission="selectBranchsWithPermission"
      />
    </van-popup>
    <van-popup
      v-model="showCar"
      position="bottom"
      close-on-click-overlay
      close-on-popstate
      safe-area-inset-bottom
      duration="0.4"
      :style="{ width: '100%', height: '90%' }"
    >
      <SelectCarsWithPermission
        :sheetType="'XD'"
        @selectCarsWithPermission="selectCarsWithPermission"
      />
    </van-popup>
  </div>
</template>

<script>
import {
  Icon,
  NavBar,
  Popup,
  Calendar,
  CellGroup,
  Button,
  Field,
  Tab,
  Tabs,
  Cell,
} from "vant";
import ViewGrabOrderSheets from "./ViewGrabOrderSheets.vue";
import SelectCustomer from '../components/SelectCustomer.vue';
import Position from '../../components/Position';
import SelectSellersWithOperRights from "../components/SelectSellerWithOperRights";
import SelectBranchsWithPermission from "../components/SelectBranchWithPermission";
import SelectSendersWithOperRights from "../components/SelectSenderWithOperRights";
import SelectCarsWithPermission from "../components/SelectCarsWithPermission"
export default {
  name: "GrabOrderSheets",
  data () {
    return {
      active: 1,
      selectShow: false,//展示右侧菜单
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      showDate: false,//弹出日期选择
      showCustomer: false,//客户选择弹出
      showSeller: false,
      showSender: false,
      showBranch: false,
      showCar: false,
      queryCondiValues: {
        startDate: "",
        endDate: "",
        supcustID: "",
        operID: "",
        isReceipted: false,
        sheetNo:"",
        supcustFlag: "",
        searchStr: "",
        regionID: "",
        currentLng: "",
        currentLat: "",

      },
      queryCondiLabels: {
        dateTimeInfo: '',
        clientName: "",
        regionName: "",
      }
    };
  },
  computed: {
    canMake () {
      return hasRight("sale.sale.orderManage.see");
    }
  },
  components: {
    "van-nav-bar": NavBar,
    "van-popup": Popup,
    "van-icon": Icon,
    "van-calendar": Calendar,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-cell": Cell,
    ViewGrabOrderSheets,
    SelectSellersWithOperRights,
    SelectBranchsWithPermission,
    SelectSendersWithOperRights,
    SelectCarsWithPermission,
    SelectCustomer
  },
  created () {

  },
  async mounted () {
    this.queryCondiValues.isReceipted = false;
    if (!this.$store.state.yjSelectCalendarCacheStore.ViewDeliveryReceiptCacheKey) {
      this.handleDate(-6, 0)
    }
    // setTimeout(()=>{
    //this.tabChange('0')
    //},300)

  },
  beforeRouteEnter (to, from, next) {
    console.log(to)
    console.log(from)
    next(vm => {
      // 通过 `vm` 访问组件实例
      console.log(vm)
      /*if(from.name=="SaleSheet" && to.query.reded_sheet_id){
        vm.$nextTick(() => {
            vm.$refs.deliveryReceiptView.removeRededRec_From_DeliveryList(to.query.reded_sheet_id);
        })
      }*/
    })
  },
  methods: {
    onBackToThisPage (fromPage, params) {
      if (fromPage == "SaleSheet"){
        if(params && params.action=="red")
          this.$refs.deliveryReceiptView.removeRededRec_From_DeliveryList(params.sheet_id);
      }
    },
    selectCarsWithPermission (value) {
      this.showCar = value.isCarShow
      this.queryCondiLabels.carID = value.branch_id;
      this.queryCondiLabels.carName = value.branch_name;
      this.queryCondiValues.carID = value.branch_id;
      this.queryCondiValues.carName = value.branch_name;
    },
    selectSendersWithOperRights (value) {
      this.showSender = value.isSenderShow;
      this.queryCondiLabels.senderID = value.oper_id;
      this.queryCondiLabels.senderName = value.oper_name;
      this.queryCondiValues.senderID = value.oper_id;
      this.queryCondiValues.senderName = value.oper_name;
    },
    selectBranchsWithPermission (value) {
      this.showBranch = value.isBranchShow;
      this.queryCondiLabels.branchID = value.branch_id;
      this.queryCondiLabels.branchName = value.branch_name;
      this.queryCondiValues.branchID = value.branch_id;
      this.queryCondiValues.branchName = value.branch_name;
    },
    async btnScanBarcode_click() {
      // if (this.sheet.approve_time) return
      const result = await this.getScanBarResult()
      /*const SUPPORT_CODE_TYPE=['EAN_13',"EAN_8","ITF"]
      if (SUPPORT_CODE_TYPE.indexOf(result.format)===-1) {
        this.$toast("请扫描8、13或14位条码")
        return
      }*/
      // this.searchStr = result.code;
      // this.btnClassView_click();
      this.queryCondiValues.sheetNo = result.code
      this.queryReceipts( this.queryCondiValues.isReceipted)
    },
    getScanBarResult() {
      return new Promise((resolve, reject) => {
        const supportFormat =  {
                Code128: true,
                Code39: true,
                Code93: true,
                CodaBar: true,
                DataMatrix: true,
                EAN13: true,
                EAN8: true,
                ITF: true,
                QRCode: false,
                UPCA: true,
                UPCE: true,
                PDF417: true,
                Aztec: true,
              }
        const androidconfig = {
              barcodeFormats:supportFormat,
              beepOnSuccess: true,
              vibrateOnSuccess: false,
              detectorSize: .6,
              rotateCamera: false,
          // preferFrontCamera: false, // iOS and Android
          // showFlipCameraButton: true, // iOS and Android
          // showTorchButton: true, // iOS and Android
          // torchOn: false, // Android, launch with the torch switched on (if available)
          // saveHistory: true, // Android, save scan history (default false)
          // prompt: "Place a barcode inside the scan area", // Android
          // resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          // orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          // disableAnimations: true, // iOS
          // disableSuccessBeep: false // iOS and Android
        }
        const iosconfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: false, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          disableAnimations: false, // iOS
          disableSuccessBeep: false // iOS and Android
        }
        let barcodeScannerAndroidConfig = {
          preferFrontCamera: false, // iOS and Android
          showFlipCameraButton: true, // iOS and Android
          showTorchButton: true, // iOS and Android
          torchOn: false, // Android, launch with the torch switched on (if available)
          saveHistory: true, // Android, save scan history (default false)
          prompt: "Place a barcode inside the scan area", // Android
          resultDisplayDuration: 1000, // Android, display scanned text for X ms. 0 suppresses it entirely, default 1500
          // formats: "", // default: all but PDF_417 and RSS_EXPANDED
          orientation: "portrait", // Android only (portrait|landscape), default unset so it rotates with the device
          disableAnimations: true, // iOS
          disableSuccessBeep: false, // iOS and Android
          barcodeFormats:supportFormat
        }


        const config = isiOS ? iosconfig : (typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? androidconfig : barcodeScannerAndroidConfig)
        const plugin = isiOS || typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined' ? cordova.plugins.barcodeScanner : cordova.plugins.mlkit.barcodeScanner
        if (isiOS) {
          plugin.scan(
            async (result) => {
              const res = { unit_type, code: result.text, format: result.format }
              resolve(res)
            },
            async (res) => {
              reject(res)
            },
            config
          );

        } else {
          const useOldPlugin = typeof cordova.plugins.mlkit?.barcodeScanner == 'undefined'
          console.log(useOldPlugin)
          if (useOldPlugin) {
            plugin.scan(

              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              },
              config
            );
          } else {
            plugin.scan(
              config,
              async (result) => {
                const res = { unit_type, code: result.text, format: result.format }
                resolve(res)
              },
              async (res) => {
                reject(res)
              }
            );
          }
        }
      })
    },
    selectSellersWithOperRights (value) {
      this.showSeller = value.isSellerShow;
      this.queryCondiLabels.sellerID = value.oper_id;
      this.queryCondiLabels.sellerName = value.oper_name;
      this.queryCondiValues.sellerID = value.oper_id;
      this.queryCondiValues.sellerName = value.oper_name;
    },
    async queryReceipts (isReceipted = false) {

      //var res = await Position.getPosition()

      let res = {};
      let params = {
        message: "需要定位权限来获取客户距离",
        key: "positionToCustom"
      }

      res = await Position.getPosition(params);
      if (res.result == "OK") {
        this.queryCondiValues.currentLng = res.longitude
        this.queryCondiValues.currentLat = res.latitude;
      }
      this.queryCondiValues.isReceipted = isReceipted;

      this.$refs.deliveryReceiptView.newQuery();
    },
    tabChange (key) {
      var isReceipted = key === '1'
      this.queryCondiValues.isReceipted = isReceipted
      this.queryReceipts(isReceipted)
    },

    computeDate (days) {
      var d = new Date();
      d.setDate(d.getDate() + days);
      var m = d.getMonth() + 1;
      return d.getFullYear() + '-' + m + '-' + d.getDate();
    },
    formatHistorySaleSheetStartDateQuery (date) {
      return `${date.getFullYear()}-${date.getMonth() + 1 - 3
        }-${date.getDate()}`;
    },
    onConfirm (date) {
      const [start, end] = date;
      this.showDate = false;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`;
      this.queryCondiValues.endDate = `${this.formatDate(end)}`;
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;

    },
    handleDate (startDay, endDay) {
      this.queryCondiValues.startDate = this.computeDate(startDay);
      this.queryCondiValues.endDate = this.computeDate(endDay);
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + "至" + this.queryCondiValues.endDate;
      this.submitSelect();
    },
    handleDateSon () {
      this.submitSelect();
    },
    selectCustomer (value) {
      this.showCustomer = value.isShow;
      this.queryCondiValues.supcustID = value.ids;
      this.queryCondiLabels.supcustName = value.titles;
    },
    //选择后执行刷新操作
    async submitSelect () {
      //var  position=new Position(isiOS)
      // const {longitude,latitude}=await position.currentPosition()

      let res = {};
      let params = {
        message: "需要定位权限来获取客户距离",
        key: "positionToCustom"
      }
      res = await Position.getPosition(params);
      console.log(res);
      //this.queryCondition.currentLng = res.longitude;
      //this.queryCondition.currentLat = res.latitude;
      this.queryCondiValues.currentLng = res.longitude;
      this.queryCondiValues.currentLat = res.latitude;

      // this.queryCondiValues.currentLng = longitude
      // this.queryCondiValues.currentLat = latitude;
      this.selectShow = false;
      console.log("newQuery")
      this.$refs.deliveryReceiptView.newQuery();
    },
    //清空选择
    cancelSelect () {
      this.selectShow = false;
      //this.queryCondiValues.operID = "";
      this.queryCondiValues.supcustID = "";
      // this.queryCondiLabels.operName = "";
      this.queryCondiLabels.supcustName = "";
      this.queryCondiLabels.carID = ""
      this.queryCondiLabels.carName = ""
      this.queryCondiLabels.senderID = ""
      this.queryCondiLabels.senderName = ""
      this.queryCondiLabels.branchID = ""
      this.queryCondiLabels.branchName = ""
      this.queryCondiLabels.sellerID = ""
      this.queryCondiLabels.sellerName = ""
      this.queryCondiValues.carID = ""
      this.queryCondiValues.carName = ""
      this.queryCondiValues.senderID = ""
      this.queryCondiValues.senderName = ""
      this.queryCondiValues.branchID = ""
      this.queryCondiValues.branchName = ""
      this.queryCondiValues.sellerID = ""
      this.queryCondiValues.sellerName = ""
      this.submitSelect();
    }
  },
};
</script>

<style lang="less" scoped>
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@posAblot: {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
};

/deep/.van-tabs--line .van-tabs__wrap {
  border-bottom: 1px solid #f2f2f2;
}
.look_box {
  height: calc(100% - 45px);
}

/deep/.van-tabs {
  height: calc(100% - 46px);
}
/deep/.van-tab__pane,
/deep/.van-tabs__content {
  height: 100%;
}
/deep/.van-tab {
  font-size: 15px;
}
.van_popup {
  h5 {
    height: 46px;
    font-size: 16px;
    font-weight: 600;
    background: #f2f2f2;
    @flex_a_j();
    .van-icon {
      position: absolute;
      top: 0;
      right: 10px;
      font-size: 20px;
      color: #666666;
      line-height: 46px;
    }
  }
  .cellgroup {
    height: calc(100% - 46px - 55px - 15px);
  }
}
.footer_button {
  width: 100%;
  height: 45px;
  margin-top: 10px;
  vertical-align: top;
  button {
    width: 100px;
    height: inherit;
    margin: 0 20px;
    vertical-align: top;
  }
}
.content {
  text-align: left;
}
.handle_date_btns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 10px;
  .date_btn {
    margin-bottom: 20px;
    flex: 50%;

    button {
      width: 60%;
      background-color: #f4f4f4;
      border: none;
      border-radius: 5px;
    }
  }
}
</style>