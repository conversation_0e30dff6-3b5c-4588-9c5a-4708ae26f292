<template>
  <div class="pages">
    <div class="serchWrapper">
      <div class="go_back" @click="gobackLeft">
        <van-icon name="arrow-left" />
      </div>
      <div class="search_content">
        <van-search id="txtSearch" v-model="searchStr" left-icon placeholder="品名/条码/助记码" @input="onSearchItem" @click="onSearchStrClick">
          <template #right-icon>
            <i class="iconfont" style="font-size:12px">&#xe63c;</i>
          </template>
        </van-search>
      </div>

      <div class="search_layout">
        <van-button style="background-color:transparent;border:none;padding:10px;margin-right:10px;" type="info" @click="btnScanBarcode_click()">
          <svg width="26px" height="26px" fill="#444">
            <use xlink:href="#icon-barcodeScan"></use>
          </svg>
        </van-button>
        <van-icon class="add_icon" style="margin-right:15px;" @click="detailedQuery" name="apps-o" />
        <van-icon v-if="editInfoItem" class="add_icon" name="plus" @click="btnAddItem_click" />
      </div>
    </div>
    <!-- <van-nav-bar title="商品档案" left-arrow @click-left="gobackLeft" safe-area-inset-top>
      <template #right>
        <div style="margin-right:50px" @click="detailedQuery"> 其他</div>
        <van-icon v-if="editInfoItem" class="add_icon" name="plus" @click="btnAddItem_click" />
      </template>
    </van-nav-bar> -->
    <van-dialog class="delete_dialog" width="320px" v-model="DeleteClassObj.deleteClass" :message="`确认删除${DeleteClassObj.class_name}吗？`" closeOnPopstate confirmButtonColor="#6389fa" closeOnClickOverlay show-cancel-button show-confirm-button @confirm="deleteClassConfirm">
    </van-dialog>
    <van-popup v-model="popupSubmitPannel" :style="{ height: '100%', width: '70%',overflowY:'auto' }" class="van_popup" position="right">
      <div style="margin-top:10px">其他条件</div>
      <div class="query">
        <div class="query-wrapper">
          <YjSelectTree
            style="height: auto;"
            ref="selectTreeRef"
            :getContainer="'.pages'"
            :target="'class'"
            :title="title"
            :confirmColor="confirmColor"
            :rangeKey="rangeKey"
            :rootNode="rootNode"
		        :idKey="idKey"
            :sonNode="sonNode"
            :parentSelectable="parentSelectable"
            :popupHeight="'85%'"
            @getRootNode="getRootNode"
            @handleConfirm="itemClassSelect"
          >
          <template #select-tree-content>
            <van-field v-model="class_name" readonly label="类别" placeholder="请选择" />
          </template>
          </YjSelectTree>
          <select-one ref="selectOne" :input-align="'left'" :getContainer="'.pages'" style="width:100%;" :readonly="true" :label="'品牌'" :placeholder="'请选择'" :hasClearBtn="false" :target="'brand'" :formFld="'brand_id'" :formNameFld="'brand_name'" :formObj="formObj"/>
          <!-- <van-field v-model="brand_name" readonly label="品牌" placeholder="请选择" @click="showBrandObj.showBrand = true" /> -->
          <div class="choose_wrapper">
            <div class="choose_text">状态</div>
            <div class="choose_content">
              <van-dropdown-menu>
                <van-dropdown-item v-model="status" :options="statusOption" />
              </van-dropdown-menu>
            </div>
          </div>
          <div class="choose_wrapper">
            <div class="choose_text">小单位价格</div>
            <div class="choose_content"><van-checkbox v-model="sUnitPriceCheck" @change="handleShowSunitPrice"/></div>
          </div>
          <div class="choose_wrapper">
            <div class="choose_text">首选排序</div>
            <van-dropdown-menu>
              <van-dropdown-item v-model="sortFld1" :options="sortFld1Options" @change="onSortFld1Change" class="custom-dropdown-item" :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
            </van-dropdown-menu>
          </div>
          <div class="choose_wrapper">
            <div class="choose_text">次选排序</div>
            <van-dropdown-menu>
              <van-dropdown-item v-model="sortFld2" :options="sortFld2Options" :disable="!sortFld1" @change="onSortFld2Change" class="custom-dropdown-item" :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
            </van-dropdown-menu>
          </div>
          <div class="choose_wrapper">
            <div class="choose_text">末选排序</div>
            <van-dropdown-menu>
              <van-dropdown-item v-model="sortFld3" :options="sortFld3Options" :disable="!sortFld1 || !sortFld2" @change="onSortFld3Change" class="custom-dropdown-item" :style="{ '--custom-van-cell-background-color': '#d3d3d3' }"/>
            </van-dropdown-menu>
          </div>
        </div>
        <div class="footer_button_wrapper">
          <van-button style="width:100px;height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc" plain type="default" @click="cancelSelect">清空
          </van-button>
          <van-button style="width:100px;height: 45px;border-radius:12px;background-color:#fff;border:1px solid #ccc" plain type="info" @click="submitSelect">确认
          </van-button>
        </div>
      </div>
    </van-popup>
    <!-- <van-popup style="overflow: hidden!important;z-index:99999!important;margin-bottom:35px" v-model="showClassObj.showClass" position="bottom" :style="{ height: '85%', width: '100%' }">
      <class-selection :parentSelectable="showClassObj.showClassFlag=='handleClass'" style="z-index:99999!important;" @itemClassSelect="itemClassSelect"></class-selection>
    </van-popup> -->
    <!-- <van-popup style="overflow: hidden!important;margin-bottom:35px" v-model="showBrandObj.showBrand" position="bottom" :style="{ height: '85%', width: '100%' }">
      <select-brand @selectBrand="selectBrand"></select-brand>
    </van-popup> -->
    <van-popup v-model="showHandleClassOptions" round :style="{ padding: '20px',width:'150px',height:'auto'}">
      <div v-if="nowHandleClass.name==='全部 '" class="popover-wrapper">
        <div v-for="(action,popoverIndex) in allaction" @click="onSelect(action,true)" :class="popoverIndex===allaction.length-1?'popover-item noline':'popover-item'">
          <van-icon style="margin-right: 10px;" :name="action.icon" />
          <div>{{ action.text }}</div>
        </div>
      </div>
      <div v-else class="popover-wrapper">
        <div v-for="(action,popoverIndex) in actions" :key="'item_' + action.text" @click="onSelect(action,false)" :class="popoverIndex===actions.length-1?'popover-item noline':'popover-item'">
          <van-icon style="margin-right: 10px;" :name="action.icon" />
          <div>{{ action.text }}</div>
        </div>
      </div>
    </van-popup>
    <van-popup @open="beforeOpenEditClass" @closed="closedEditClass" v-model="showAddOrEditClass" round :style="{ padding: '20px',width:'310px',height:'auto'}">
      <add-or-edit-goods-class ref="handleClass" :addForAll="addForAll" :useStoreClass="true" :classTitle="classTitle" :isNewRecord="isNewRecord" @saveClassNode="saveClassNode" @editClassNode="editClassNode" @selectMotherClass="selectMotherClass" @selectClassBrand="selectClassBrand" @closeAddOrEditClass="closeAddOrEditClass">
      </add-or-edit-goods-class>
    </van-popup>
    <div class="public_box2">
      <item-archives ref="itemsView" @handleClickEdit="handleClickEdit" :searchItem="itemName" :queryParams="queryParams">
      </item-archives>
      <!--      <item-archivesClass :classID="Number(queryParams.class_id) "></item-archivesClass>-->
    </div>
    <!-- <div :offset-bottom="0" position="bottom" class="class_box_bottom">
      <van-icon name="records" size="25" />
      <van-icon name="add-o" size="25"/>
    </div> -->
  </div>
</template>

<script>
import { NavBar, Button, Icon, Popup, Field, DropdownMenu, DropdownItem, Dialog, Search, Toast,Checkbox } from 'vant'
import ClassSelection from "../components/ItemClass"
import SelectBrand from '../components/SelectBrand'
import ItemArchives from '../components/ItemArchives'
// import AddOrEditClass from '../components/AddOrEditClass'
import AddOrEditGoodsClass from '../components/AddOrEditGoodsClass.vue'
import ItemArchivesClass from '../components/ItemArchivesClass'
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";
import SelectOne from "../components/SelectOne.vue";
import { ImagePreview } from 'vant'
import Vue from 'vue';
import { GetAttrItemInfos, DeleteClass } from "../../api/api"

Vue.use(Popup);
export default {
  name: "GoodsArchives",
  data() {
    return {
      class_id: '',
      class_name: '',
      brand_id: '',
      brand_name: '',
      status: '1',
      sort_type:"default",
      statusOption: [
        { text: '全部', value: '' },
        { text: '正常', value: '1' },
        { text: '禁用', value: '0' }
      ],
      sortFld1: "order_index",
      sortFld2: "item_name",
      sortFld3: "none",
      sortFld1Options: [
        { text: '商品名称', value: 'item_name' },
        { text: '序号', value: 'order_index' },
        { text: '后创建在前', value: 'recent_create' },
        // { text: '有库存在前', value: 'stock' },
        // { text: '无库存在前', value: 'no_stock' },
        // { text: '库存多在前', value: 'more_stock' },
        // { text: '库存少在前', value: 'less_stock' },
      ],

      showSelect: false,
      showMotherClass: false,
      popupSubmitPannel: false,
      showDeleteClass: false,
      // showClass: false,
      // showBrand: false,
      showAddOrEditClass: false,
      isNewRecord: false,
      classTitle: '',
      formObj:{},
      queryParams: {},
      showClassObj: {
        showClass: false,
        showClassFlag: ""
      },
      showBrandObj: {
        showBrand: false,
        showBrandFlag: ""
      },
      DeleteClassObj: {
        deleteClass: false,
        class_name: '',
        class_id: '',
        operKey: ''
      },
      itemName: '',
      searchStr: '',
      showHandleClassOptions: false,
      nowHandleClass: {},
      allaction: [
        { text: '添加下级类', icon: 'add-o' }
      ],
      actions: [
        { text: '编辑类', icon: 'records' },
        { text: '添加下级类', icon: 'add-o' },
        { text: '添加同级类', icon: 'add-o' },
        { text: '删除类', icon: 'delete-o' },
      ],
      addForAll: false,
      title:'类别选择',
			rangeKey: 'name',
      idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			parentSelectable: true,
      foldAll: false,
			confirmColor:'#e3a2a2',
      rootNode:{},
      sUnitPriceCheck:false
    }

  },
  computed: {
    sortFld2Options() {
    let filteredOptions = [...this.sortFld1Options, { text: '无', value: 'none' }]
    return this.optionsFilter(filteredOptions, this.sortFld1);
    },
    sortFld3Options() {
    let filteredOptions = [...this.sortFld2Options]
    return this.optionsFilter(filteredOptions, this.sortFld2);
    },
    editInfoItem() {
      return hasRight('info.infoItem.edit')
    },
    createAuthority() {
      let auth = getRightValue('info.infoItem.create')
      if (auth == "false") {
        return false
      } else {
        return true
      }
    },
  },
  // watch:{
  //   '$route'(to,from){
  //     if(from.path == '/GoodsArchivesSon'){
  //             this.onSearchItem()
  //     }
  //   }
  // },
  components: {
    "van-nav-bar": NavBar,
    [Dialog.Component.name]: Dialog.Component,
    "van-icon": Icon,
    "van-field": Field,
    "van-button": Button,
    "van-dropdown-menu": DropdownMenu,
    "van-dropdown-item": DropdownItem,
    "van-checkbox":Checkbox,
    "van-search": Search,
    // [ImagePreview.Component.name]: ImagePreview.Component,
    "class-selection": ClassSelection,
    "select-brand": SelectBrand,
    "item-archives": ItemArchives,
    "item-archivesClass": ItemArchivesClass,
    // "add-or-edit-class": AddOrEditClass,
    YjSelectTree,
    SelectOne,
    AddOrEditGoodsClass
  },
  activated() {
    window.sayCode = (result) => {
      this.pageSayCode(result)
    };
  },
  mounted() {
    // 从 localStorage 恢复用户选择的排序和筛选条件
    console.log('组件被挂载');

    // 添加租户检查，防止跨租户缓存问题
    const currentOperKey = this.$store.state.operKey;
    const cachedOperKey = localStorage.getItem('cached_operKey');

    if (currentOperKey !== cachedOperKey) {
      // 如果租户发生变化，清空相关缓存
      console.log('检测到租户变化，清空缓存');
      localStorage.removeItem('class_id');
      localStorage.removeItem('class_name');
      localStorage.removeItem('brand_id');
      localStorage.removeItem('brand_name');
      localStorage.removeItem('status');
      localStorage.removeItem('sortFld1');
      localStorage.removeItem('sortFld2');
      localStorage.removeItem('sortFld3');
      localStorage.setItem('cached_operKey', currentOperKey);
    }

    this.class_id = localStorage.getItem('class_id') || '';
    this.class_name = localStorage.getItem('class_name') || '';
    this.formObj.brand_id = localStorage.getItem('brand_id') || '';
    this.formObj.brand_name = localStorage.getItem('brand_name') || '';
    this.status = localStorage.getItem('status') || '1';
    this.sortFld1 = localStorage.getItem('sortFld1') || 'order_index';
    this.sortFld2 = localStorage.getItem('sortFld2') || 'item_name';
    this.sortFld3 = localStorage.getItem('sortFld3') || 'none';

    this.$bus.$on("handleItemImagePreview", (obj) => {
      if(!obj.other) {
        obj.other = [obj.main]
      }

      ImagePreview({
        images: obj.other,
        closeable: true
      })
    })

    // 更新 queryParams
    this.updateQueryParams();

    window.sayCode = (result) => {
      this.pageSayCode(result)
    }

  },
  deactivated() {
  console.log('组件被停用（进入缓存）');
   },
   beforeDestroy() {
  console.log('组件将被销毁');
   },

  methods: {
    onSortFld1Change(value) {
      this.checkAndUpdateSortFld(value, 'sortFld2');
      this.checkAndUpdateSortFld(value, 'sortFld3');
      this.sortFld1 = value;
    },
    onSortFld2Change(value) {
      this.checkAndUpdateSortFld(value, 'sortFld3');
      this.sortFld2 = value;
    },
    onSortFld3Change(value) {
      this.sortFld3 = value;
    },
    checkAndUpdateSortFld(value, fieldToCheck) {
      const groups = {
        no_stock: ['stock'],
        stock: ['no_stock'],
        more_stock: ['less_stock'],
        less_stock: ['more_stock']
      };
      if (value === this[fieldToCheck] || (groups[value] && groups[value].includes(this[fieldToCheck]))) {
        this[fieldToCheck] = 'none';
      }
    },
    optionsFilter(filteredOptions, excludeSort) {
    switch (excludeSort) {
      case 'item_name':
        return filteredOptions.filter(option => option.value !== 'item_name');
      case 'order_index':
        return filteredOptions.filter(option => option.value !== 'order_index');
      case 'recent_create':
        return filteredOptions.filter(option => option.value !== 'recent_create');
      case 'stock':
      case 'no_stock':
        return filteredOptions.filter(option => !['stock', 'no_stock'].includes(option.value));
      case 'more_stock':
      case 'less_stock':
        return filteredOptions.filter(option => !['more_stock', 'less_stock'].includes(option.value));
      /* case 'higher_price':
      case 'lower_price':
        return filteredOptions.filter(option => !['higherPrice', 'lowerPrice'].includes(option.value)); */
      default:
        return filteredOptions;
       }
    },
    handleShowSunitPrice(){
      this.$refs.itemsView.handleShowSunitPrice(this.sUnitPriceCheck)
      localStorage.setItem('sUnitPriceShow', this.sUnitPriceCheck)
    },
    handleClickEdit(item, flag) {
      // console.log("选中",item)
      this.nowHandleClass = item
      this.showHandleClassOptions = flag
    },
    saveClassNode(classObj) {
      this.$refs.itemsView.$refs.classList.saveClassNode(classObj)
      // console.log("classObj",classObj)
      // console.log(this.$refs.itemsView.$refs.classList)
    },
    editClassNode(classObj) {
      // console.log("editClassObjid",classObjId)
      // console.log("能取到吗",this.$refs.itemsView.$refs.classList.editClassNode(classObjId))
      this.$refs.itemsView.$refs.classList.editClassNode(classObj)
    },
    deleteClassNode(classObjId) {
      console.log("editClassObjid", classObjId)
      this.$refs.itemsView.$refs.classList.deleteClassNode(classObjId)
      // console.log("能取到吗",this.$refs.itemsView.$refs.classList.editClassNode(classObjId))

    },
    onSelect(action, flag) {
      console.log("action", action)
      this.showHandleClassOptions = false
      this.addForAll = flag
      if (action.text === '编辑类') {
        this.classEdit()
      } else if (action.text === '添加同级类') {
        this.classAdd("same level")
      } else if (action.text === '添加下级类') {
        this.classAdd("lower level")
      } else if (action.text === "删除类") {
        this.classDelete(this.nowHandleClass.id, this.nowHandleClass.name)
      }
      // console.log("item",item)
    },
    classEdit() {
      this.isNewRecord = false
      this.classTitle = '编辑类别'
      this.showAddOrEditClass = !this.showAddOrEditClass
    },
    classAdd(type) {
      if (type === "same level") {
        this.classTitle = '添加同级类别'
      } else {
        this.classTitle = '添加下级类别'
      }
      this.isNewRecord = true
      this.showAddOrEditClass = !this.showAddOrEditClass
    },
    classDelete(id, name) {
      this.DeleteClassObj.deleteClass = !this.DeleteClassObj.deleteClass,
      this.DeleteClassObj.operKey = this.$store.state.operKey,
      this.DeleteClassObj.class_id = id.toString(),
      this.DeleteClassObj.class_name = name
    },
    onSearchItem() {
      this.itemName = this.searchStr
      if (this.inputTimer) clearTimeout(this.inputTimer)
      this.inputTimer = 0
      this.inputTimer = setTimeout(() => {
        this.inputTimer = 0
        this.$refs.itemsView.itemName = this.itemName
      }, 300)
    },
    pageSayCode(result) {
      console.log("result", result)
      this.searchStr = result
      this.onSearchItem()
    },
    // queryData(invokeBy) {
    // try {
    //   // 确保 queryCondition 包含所有需要的参数
    //   this.queryCondition.sortType = this.sort_type;
    //   this.queryCondition.sortFld1 = this.sortFld1;
    //   this.queryCondition.sortFld2 = this.sortFld2;
    //   this.queryCondition.sortFld3 = this.sortFld3;

    //   // 提交查询条件到 store
    //   this.$store.commit('clearPartOfSheetState', {});

    //   // 调用 itemList 的 newQuery 方法，并传递 queryCondition 和 invokeBy 参数
    //   this.$refs.itemList.newQuery(this.queryCondition, invokeBy);
    // } catch (e) {
    //   alert('查询异常1:' + e.message);
    // }
  // },
    onSearchStrClick() {
      this.$refs.itemsView.itemName = ''
      this.searchStr = ''
      this.preSearchStr = ''
    },
    async btnScanBarcode_click() {
      const res = await this.scanBarcode()
      if (!res.code) {
        return
      }
      this.searchStr = res.code
      this.onSearchItem()
    },
    async btnAddItem_click() {
      if(!this.createAuthority){
        Toast.fail("暂无新增权限")
        return
      }
      const res = await GetAttrItemInfos(this.queryParams)
      if (res.result === 'OK') {
        let goodsDetailData = []
        goodsDetailData[0] = {}
        goodsDetailData[0].attrOptions = res.attrOptions
        goodsDetailData[0].availAttributes = res.availAttributes
        window.g_fromPage = this
        console.log(goodsDetailData[0])
        if (res.availAttributes) {
          this.$router.push({ path: '/GoodsArchivesSon', query:goodsDetailData[0]})
        } else {
          this.$router.push({ path: '/GoodsArchivesSon' })
        }
      }
    },
    // hidePopover(e) {
    //   const classParentPopover=document.getElementById("classParentPopover")
    //   const classPopover=document.getElementById("classPopover")
    //   if(classParentPopover||classPopover) {
    //     if(!classParentPopover.contains(e.target)||!classPopover.contains(e.target)) {
    //       this.$bus.$emit("closeClassPopover")
    //     }else {
    //       return
    //     }
    //   }else {
    //     return
    //   }
    // },
    addRow(row) {
      this.$refs.itemsView.addRow(row)
    },
    gobackLeft() {
      myGoBack(this.$router)
      this.$store.commit('motherId', this.$store.state.AllItemClassId)
    },
    detailedQuery() {
      this.popupSubmitPannel = !this.popupSubmitPannel;
    },
    getRootNode(node) {
      this.rootNode=node
    },
    itemClassSelect(obj) {
      // this.showClassObj.showClass = obj.isShow
      let ItemClassObj=this.$store.state.ItemClassObj
      if (this.showClassObj.showClassFlag === 'handleClass') {
        if(obj.path.includes(ItemClassObj.class_id)){
            Toast.fail("父级类别不可选择自身及其子类")
            return
          }
        // this.showMotherClass=obj.isShow
        this.$refs.handleClass.onMotherClassSelect(obj)
      } else {
        // if (obj.itemObjs !== "") {
        //   this.class_id = obj.itemObjs.ids
        //   this.class_name = obj.itemObjs.titles
        // } else {
        //   this.class_id = ''
        //   this.class_name = ''
        // }
        if(obj.length>0){
          this.class_id =obj[0].id,
          this.class_name=obj[0].name
        } else {
          this.class_id = ''
          this.class_name = ''
        }
      }
      this.$refs.selectTreeRef.handleCancel()
    },
    // itemMotherClassSelect(obj) {
    //   this.showMotherClass=obj.isShow
    //   this.$refs.addOrEditClass.onMotherClassSelect(obj)
    // },
    selectBrand(obj) {
      this.showBrandObj.showBrand = obj.isBrandShow
      if (this.showBrandObj.showBrandFlag === 'handleClassBrand') {
        this.$refs.handleClass.onBrandSelect(obj)
      } else {
        this.brand_id = obj.brand_id
        this.brand_name = obj.brand_name
      }
    },
    selectMotherClass(obj) {
      this.showClassObj.showClass = obj.isShow
      this.showClassObj.showClassFlag = obj.showClassFlag
    },
    selectClassBrand(obj) {
      this.showBrandObj.showBrand = obj.isShow
      this.showBrandObj.showBrandFlag = obj.showBrandFlag
    },
    // saveClass(){
    //   this.$refs.itemsView.saveClass()
    // },
    closeAddOrEditClass() {
      this.showAddOrEditClass = false
    },
    beforeOpenEditClass() {
      if (!this.isNewRecord) {
        this.$nextTick(() => {
          this.$refs.handleClass.editClass(this.isNewRecord)
        })
      }
      else {
        this.$nextTick(() => {
          this.$refs.handleClass.addClass(this.isNewRecord)
        })
      }
      // if(this.classTitle==='添加下级类别'&&this.addForAll){
      //   let ItemClassObj=this.$store.state.ItemClassObj
      //   let obj={
      //     mother_class_path:ItemClassObj.mother_class_path,
      //     mother_id:ItemClassObj.mother_id,
      //     mother_name:ItemClassObj.mother_name
      //   }
      //   this.$refs.handleClass.addLowerClassToAll(obj)
      // }
    },
    closedEditClass() {
      this.$nextTick(() => {
        this.$refs.handleClass.clearClassEdit()
      })
    },

    cancelSelect() {
      this.class_id = "";
      this.class_name = "";
      this.brand_id = "";
      this.formObj.brand_id = '';
      this.brand_name = "";
      this.formObj.brand_name = '';
      this.status = '1';
      this.sortFld1 = "order_index";
      this.sortFld2 = "item_name";
      this.sortFld3 = "none";
      this.$refs.selectOne && this.$refs.selectOne.clear_click?.();

      // 清空 localStorage 中的相关信息
      localStorage.removeItem('class_id');
      localStorage.removeItem('class_name');
      localStorage.removeItem('brand_name');
      localStorage.removeItem('brand_id');
      localStorage.removeItem('status');
      localStorage.removeItem('sortFld1');
      localStorage.removeItem('sortFld2');
      localStorage.removeItem('sortFld3');

      // 更新查询参数为默认值
      this.updateQueryParams();
    },

    // 添加更新查询参数的方法
    updateQueryParams() {
      this.queryParams = {
        brand_id: this.formObj.brand_id || null,
        brand_name: this.formObj.brand_name || null,
        class_name: this.class_name || "0",
        class_id: this.class_id || "0",
        status: this.status === "" ? "all" : this.status,
        sortFld1: this.sortFld1,
        sortFld2: this.sortFld2,
        sortFld3: this.sortFld3
      };
      console.log("更新查询参数:", this.queryParams);
    },

    submitSelect() {
      this.popupSubmitPannel = false;

      // 保存选择到 localStorage，同时保存当前租户信息
      localStorage.setItem('class_id', this.class_id || '');
      localStorage.setItem('class_name', this.class_name || '');
      localStorage.setItem('brand_id', this.formObj.brand_id || '');
      localStorage.setItem('brand_name', this.formObj.brand_name || '');
      localStorage.setItem('status', this.status || '1');
      localStorage.setItem('sortFld1', this.sortFld1);
      localStorage.setItem('sortFld2', this.sortFld2);
      localStorage.setItem('sortFld3', this.sortFld3);
      localStorage.setItem('cached_operKey', this.$store.state.operKey);

      // 更新查询参数
      this.updateQueryParams();
    },

  // 校验排序字段，确保传递给后端的是合法的值
  validateSortField(field, defaultValue) {
    const validSortFields = ["item_name", "order_index", "recent_create", "stock", "no_stock", "more_stock", "less_stock", "none"];
    return validSortFields.includes(field) ? field : defaultValue;
  },


    // onbeforeClose(action, done) {
    //   if (action === "confirm") {
    //    return done(false);
    //   } else {
    //    return done();
    //   }
    // },
    async deleteClassConfirm() {
      await DeleteClass({
        class_id: this.DeleteClassObj.class_id,
        operKey: this.DeleteClassObj.operKey
      }).then(async res => {
        console.log(res)
        if (res.result === 'OK') {
          this.deleteClassNode(res.class_id)
        } else {
          console.log("sssss")
          await Toast.fail(res.result)
        }
      })
    }
  },
  // beforeRouteEnter(to,from,next){
  //   next(vm => {
  //     if(from.name=='GoodsArchivesSon'){
  //       vm.$refs.itemsView.$refs.classList.onloadClass()
  //     }
  //   })
  // }
}
</script>

<style lang="less" scoped>
.serchWrapper {
  // background-color: #eee;
  background-image: linear-gradient(to bottom, #fff 0%, #eee 100%);
  height: 46PX;
  //border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  .go_back {
    flex: 1;
    text-align: left;
    padding-left: 20PX;
  }
  .search_content {
    flex: 10;
    padding-left: 20PX;
    padding-right: 10PX;
  }
  .search_layout {
  display: flex;
  justify-content: space-evenly;
  align-items: flex-end; /* 让图标往下 */
  height: 50px; /* 确保有足够高度 */
}

.search_layout > van-button,
.search_layout > van-icon {
  position: relative;
  top: 5px; /* 或者用 transform */
  transform: translateY(6px);
}
  }


.van-search {
  background: transparent;
  padding: 0;
  .van-search__content {
    background: transparent;
    padding-left: 0PX;
  }
  .van-cell {
    padding-right: 0PX;
  }
  /deep/.van-field__body {
    border-bottom: 1PX solid #e6e6e6 !important;
    width: 100%;
    margin: auto;
    font-size: 15PX;
  }
  /deep/#txtSearch {
    height: 20PX;
  }
  /deep/.van-field__right-icon {
    height: 24PX;
    line-height: 24PX;
  }
}
.iconfont {
  color: #bbb;
}
/deep/ .van-dropdown-menu__bar {
  height: 24px !important;
}

@flex_acent_jc: {
  display: flex;
  align-items: center;
  justify-content: center;
};

@flex_acent_jb: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};

.add_icon {
  font-size: 25PX;
  line-height: 46PX;
  margin-top: 5PX;
}

.query {
  height: 85%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.query-wrapper {
  height: 70%;
  display: flex;
  flex-direction: column;
  justify-content: space-start;
}

.footer_button_wrapper {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.choose_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.choose_text {
  flex: 1;
  text-align: left;
  padding-left: 10px; /* 添加左内边距 */
}

.van-dropdown-menu {
  flex: 1;
  text-align: right;
}

.custom-dropdown-item {
  text-align: right;
}

.class_box_bottom {
  box-sizing: border-box;
  padding: 0 5px;
  position: absolute;
  display: flex;
  justify-content: space-between;
  flex-direction: row;
  width: 35%;
  height: 30px;
  line-height: 30px;
  background-color: #fff;
  bottom: 1rem;
  .van-icon {
    line-height: 30px;
  }
}
::v-deep .van-dialog {
  transition: fade;
}
::v-deep .van-dialog__message {
  font-size: 18px;
}
.popover-wrapper {
  width: 100%;
  display: flex;
  font-size: 20px;
  flex-direction: column;
  box-sizing: border-box;
  justify-content: flex-start;
  .popover-item {
    border-bottom: 1px solid #d8cbcb;
    height: 50px;
    display: flex;
    padding: 0 10px;
    box-sizing: border-box;
    align-items: center;
    justify-content: flex-start;
  }
  .noline {
    border-bottom: 0;
  }
}
</style>
