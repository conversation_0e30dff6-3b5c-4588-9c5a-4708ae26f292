# getNativePosition 方法优化说明

## 问题描述
原来的 `getNativePosition` 方法存在位置不刷新的问题，总是返回缓存的旧位置，需要过几十秒后才会刷新。

## 原方法的问题
1. **优先使用缓存**: 首先检查 `curLocation`，如果存在就直接返回
2. **使用系统缓存**: 使用 `getLastKnownLocation()` 获取系统缓存位置
3. **立即返回**: 不等待新的位置更新就立即返回结果
4. **缺乏超时**: 没有合理的超时处理机制

## 解决方案

### 修改文件
- **文件路径**: `platforms\android\app\src\main\java\com\lai\geolocation\baidu\GeolocationPlugin.java`

### 主要改动
1. **保留原方法**: 将原方法注释并重命名为 `getNativePosition_OLD`，便于对比
2. **重写新方法**: 创建新的 `getNativePosition` 方法，解决缓存问题

### 新方法的核心改进

#### 1. 使用 `requestSingleUpdate`
```java
// 请求单次位置更新，不使用缓存
mLocationManager.requestSingleUpdate(locationProvider, singleLocationListener, null);
```

#### 2. 优先GPS定位
```java
if (providers.contains(LocationManager.GPS_PROVIDER)) {
    locationProvider = LocationManager.GPS_PROVIDER;
    Log.d(TAG, "使用GPS定位");
} else if (providers.contains(LocationManager.NETWORK_PROVIDER)) {
    locationProvider = LocationManager.NETWORK_PROVIDER;
    Log.d(TAG, "使用网络定位");
}
```

#### 3. 一次性监听器
```java
LocationListener singleLocationListener = new LocationListener() {
    @Override
    public void onLocationChanged(Location location) {
        // 获取到位置后立即返回并停止更新
        BDLocation bdLocation = toBDLocation(location);
        JSONArray message = new MessageBuilder(bdLocation).build();
        callback.success(message);
        mLocationManager.removeUpdates(this);
    }
    // ... 其他方法
};
```

#### 4. 超时机制
```java
cordova.getThreadPool().execute(new Runnable() {
    @Override
    public void run() {
        try {
            Thread.sleep(10000); // 等待10秒
            // 超时后返回最后已知位置
            Location lastLocation = mLocationManager.getLastKnownLocation(locationProvider);
            if (lastLocation != null) {
                // 返回最后已知位置
            } else {
                callback.error("定位超时且无最后已知位置");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
});
```

## 新旧方法对比

| 特性 | 原方法 | 新方法 |
|------|--------|--------|
| 位置来源 | 缓存位置 | 实时位置 |
| 更新方式 | `requestLocationUpdates` | `requestSingleUpdate` |
| 超时处理 | 无 | 10秒超时 |
| 资源管理 | 手动管理 | 自动清理 |
| 响应速度 | 立即（但可能是旧位置） | 稍慢（但是最新位置） |
| 准确性 | 低（缓存位置） | 高（实时位置） |

## 使用效果

### 修改前
- 总是返回缓存的旧位置
- 位置可能是几分钟甚至几小时前的
- 用户感觉定位不准确

### 修改后
- 每次都获取最新的实时位置
- 10秒内返回结果（新位置或最后已知位置）
- 用户体验明显改善

## 测试建议

1. **连续定位测试**: 多次点击定位按钮，验证每次都能获取新位置
2. **移动测试**: 改变位置后再定位，验证能获取到新的位置
3. **超时测试**: 在信号不好的地方测试超时机制
4. **权限测试**: 测试没有定位权限时的错误处理

## 注意事项

1. **权限要求**: 需要 `ACCESS_FINE_LOCATION` 或 `ACCESS_COARSE_LOCATION` 权限
2. **GPS开启**: 建议用户开启GPS以获得更准确的位置
3. **网络连接**: 网络定位需要网络连接
4. **电量消耗**: 实时定位会消耗更多电量，但单次请求影响较小

## 编译部署

修改完成后，需要重新编译应用：
```bash
cordova build android
```
