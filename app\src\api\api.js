// 引入axios
import axios from "axios"
import store from "../store/store"
import { Toast, Overlay } from "vant"
import Vue from "vue"
import globalVars from "../static/global-vars"
Vue.use(Toast)
Vue.use(Overlay)
// import md5 from 'js-md5';
// import _this from '../main.js';
// import {_debounce} from "@/utils"
axios.defaults.timeout = 10 * 1000 //超时时间 60秒
// let token = localStorage.getItem('userInfo')?JSON.parse(localStorage.getItem('userInfo')).token:''
// http request拦截器 添加一个请求拦截器
axios.interceptors.request.use(
  function(config) {
    if (
      config.url.startsWith("http://api.map.baidu.com/") ||
      config.url.startsWith("https://api.map.baidu.com/") ||
      config.url.startsWith("https://restapi.amap.com/")
    ) {
      return config
    }
    let operIds = store.state.operKey ? store.state.operKey : ""
    var tm=new Date().getTime()
    config.url = `${config.url}?operKey=${operIds}&sys_tm=${tm}`
    return config
  },
  function(error) {
    return Promise.reject(error)
  }
)

// 添加响应拦截器
/*axios.interceptors.response.use(function(response) {
    return response;
}, function(error) {

    let errStatus = (error.response && error.response.status) ? error.response.status.toString() : '';
    if (errStatus == "404") {
        console.log(404)
            // eslint-disable-next-line no-undef
        // _this.$message({
        //   offset: 100,
        //   message: '无法找到指定位置的资源',
        //   type: "error"
        // })
    }
    if (errStatus.substr(0, 1) == "5") {
        // eslint-disable-next-line no-undef
        // _this.$message({
        //   offset: 100,
        //   message: '服务器异常',
        //   type: "error"
        // })
    }
    if (errStatus.substr(0, 1) == "4" && errStatus != "404") {
        // eslint-disable-next-line no-undef
        // _this.$message({
        //   offset: 100,
        //   message: '访问异常',
        //   type: "error"
        // })
    }
    if (errStatus == "") {
        // eslint-disable-next-line no-undef
        // _this.$message({
        //   offset: 100,
        //   message:error.message ,
        //   type: "error"
        // })
    }

    return Promise.reject(error);
});*/
let apiTalk = process.env.VUE_APP_API_URL // 接口服务地址 在.env.js进行配置
let apiTestTalk = process.env.VUE_APP_API_TEST_URL
let NODE_ENV = process.env.NODE_ENV

export const SetApiServer = (param) => {
  if (param.substr(param.length - 1, 1) != "/") param += "/"
  apiTalk = param
}
export const GetApiServer = (param) => {
  return apiTalk
}
if (store.state.releaseType == "test") {
  SetApiServer(apiTestTalk)
}

// 登录-获取公司列表
function GetErrorMsg(error) {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    console.log(error.response.data)
    console.log(error.response.status)
    console.log(error.response.headers)
    return {
      result: "fail",
      msg: "服务器返回错误码,status " + error.response.status,
    }
  } else if (error.request || !error.message) {
    // The request was made but no response was received
    // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
    // http.ClientRequest in node.js
    console.log(error)
    console.log(error.message)
    return { result: "fail", msg: "网络连接失败",message:"网络连接失败" }
  } else{
    // Something happened in setting up the request that triggered an Error
    console.log("Error", error.message)
    return { result: "fail", msg: error.message ,message: error.message }
    //
  }
}
//从json中获取地址码
window.g_isQueryingApi=false
Vue.prototype.isQueryingApi = function(){
  if(window.g_isQueryingApi && window.g_lastQueryTime){
     var diff= new Date() - window.g_lastQueryTime
     if(diff>10000) {
        window.g_isQueryingApi = false
     }
  }
  return window.g_isQueryingApi
}

let loadingTime = null
//http://api.map.baidu.com/weather/v1/?district_id=320113&data_type=all&ak=1BhKVhevxg0uX1OuZs33hrYnu57UcCyD



export const webApiRequest = (method, url, params, setting, paramsType = 'json') => {
  window.g_isQueryingApi = true
  window.g_lastQueryTime = new Date()

  // 设置默认参数
  if (!setting) setting = {}
  if (setting.showLoading == undefined) setting.showLoading = false
  if (setting.msBeforeShowLoading == undefined) setting.msBeforeShowLoading = 300
  if (setting.overlay == undefined) setting.overlay = false
  if (setting.retryTimeOut == undefined) setting.retryTimeOut = 10000
  if (setting.retryTimes == undefined) setting.retryTimes = 20
  if (setting.retryThreshold == undefined) setting.retryThreshold = 2000
  if (setting.retryDelay == undefined) setting.retryDelay = 600
  if (setting.useCoolieUri == undefined) setting.useCoolieUri = false
  if (setting.apiTalkUri == undefined) setting.apiTalkUri = ''


  let timeout =15000// axios.defaults.timeout
  if (setting.timeout ) timeout = setting.timeout

  // 处理 GET 和 POST 参数
  let requestConfig = {}
  if (method.toUpperCase() === 'GET') {
    requestConfig = { params: params ,timeout:timeout}
  } else if (paramsType === 'form') {
    // 如果是 POST 表单形式，转换为 FormData
    let formData = new FormData()
    for (let key in params) {
      formData.append(key, params[key])
    }
    requestConfig = formData
  } else {
    // 默认 JSON 格式
    requestConfig = params
  }

  // 显示加载提示
  if (setting.showLoading) {
    loadingTime = setTimeout(() => {
      Toast.clear()
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: timeout,
        overlay: setting.overlay,
        style: { backgroundColor: "#000" },
      })
      setTimeout(() => {
        $(".van-overlay").css("background-color", "#00000040")
      }, 1)
    }, setting.msBeforeShowLoading)
  }

  const startTime = new Date().getTime()

  // 定义发送请求的函数，并传递当前的重试次数
  const makeRequest = (retryAttempts) => {
    const reqTime = new Date().getTime()
    var destUri = apiTalk + url
    if (setting.useCoolieUri ){
       if(!globalVars.coolieUri ) globalVars.coolieUri="https://coolies.yingjiang.co/"
       destUri = globalVars.coolieUri + url
    }
    else if(setting.apiTalkUri && NODE_ENV === 'production') {
       destUri = setting.apiTalkUri + '/' + url
    }


    // 选择使用的请求方法
    let axiosRequest
    if (method.toUpperCase() === 'GET') {
     // axiosRequest = axios.get(destUri, { ...requestConfig })
      axiosRequest = axios.get(destUri, requestConfig)

    } else {
      axiosRequest = axios.post(destUri, requestConfig,{timeout:timeout})
    }

    return axiosRequest.then((res) => {
        window.g_isQueryingApi = false
        if (setting.showLoading) {
          clearTimeout(loadingTime)
          Toast.clear()
        }
        if (res.data.result === "PwdError") {
          Toast.fail("密码错误,请重新登录")
          return res.data
        }
        if (res.data.result === "bug") {
          Toast.fail(res.data.msg)
          return res.data
        }
        return res.data
      })
      .catch((error) => {
        window.g_isQueryingApi = false

        const currentTime = new Date().getTime()
        const elapsedTime = currentTime - reqTime
        const elapsedTimeAll = currentTime - startTime

        // 判断是否为网络错误并且在规定时间内发生，如果是则重试

        if (error.message && (error.code === "ECONNABORTED" || error.message.includes("Network Error")|| error.message.includes("404"))
          && elapsedTime < setting.retryThreshold
          && elapsedTimeAll < setting.retryTimeOut)
        {
          if (retryAttempts > 0)
          {
            console.log(`网络错误，重试中... 剩余重试次数: ${retryAttempts}`)
            return new Promise((resolve) => {
              setTimeout(() => {
                resolve(makeRequest(retryAttempts - 1)) // 递归重试
              }, setting.retryDelay)
            })
          }
        }

        if (setting.showLoading) {
          clearTimeout(loadingTime)
          Toast.clear()
        }
        console.log('api error:',error)
        // 处理错误消息
        let msg = ""
        if (error.code === "ECONNABORTED") {
          msg = "网络连接超时,请稍后重试"
        }
        else if (error.message.includes("Network Error")) {
          msg = "网络连接失败了,请稍后重试"
        }
        else if (error.message.includes("timeout")) {
          msg = "连接超时,请稍后重试"
        }
        else if (error.message.includes("404")) {//当后台报错
          msg = "连接服务失败,请稍后重试"
        }
        else if (error.response ) {
          if(error.response.data && error.response.data.message)
             msg = '服务器返回错误:' + error.response.status + error.response.data.message
          else
             msg = '服务器返回错误:' + error.response.status
        } else {
             msg = "连接失败，请稍后再试"+error.message
        }
        Toast.fail(msg)
        return {result:'fail',msg:msg,message:msg}
      })
  }

  // 发起请求，并传递当前时间戳和重试次数
  return makeRequest(setting.retryTimes)
}
export const webApiGet_retry = (url, params, setting) => {

  return webApiRequest('GET', url, params, setting, 'json')
}
export const webApiPost_retry = (url, params, setting) => {

  return webApiRequest('POST', url, params, setting, 'json')
}
export const webApiFormPost_retry = (url, params, setting) => {

  return webApiRequest('POST', url, params, setting, 'form')
}


export const webApiGet = (
  url,
  params,
  showLoading = false,
  timeOutSetting = 300,
  overlay = false
) => {
  window.g_isQueryingApi=true
  window.g_lastQueryTime=new Date()
  var timeout=axios.defaults.timeout

  var realParams=null

  if(params){

    realParams={params: params}
    if(params.api_timeout) {
      realParams.timeout=params.api_timeout
      delete params.api_timeout
      timeout=realParams.timeout

    }
  }

  // 默认300
  if (showLoading) {
    loadingTime = setTimeout(() => {
      Toast.clear()
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: timeout,
        overlay: overlay,
        style: { backgroundColor: "#000" },
      })
      setTimeout(() => {
        $(".van-overlay").css("background-color", "#00000040")
      }, 1)
    }, timeOutSetting)
  }

  return axios
  .get(apiTalk + url, realParams)
  .then((res) => {
    //if(loadingTime){clearTimeout(loadingTime);loadingTime=null;}
    window.g_isQueryingApi=false
    if (showLoading) {
      Toast.clear()
    }
    if (res.data.result == "PwdError") {
      Toast.fail("密码错误,请重新登录")
      return res.data
    }
    if (res.data.result == "bug") {
      Toast.fail(res.data.msg)
      return res.data
    }
    return res.data;
  })
  .catch((error) => {
    if (showLoading) {
      Toast.clear()
    }
    window.g_isQueryingApi=false
    if (error.code === 'ECONNABORTED' || error.message.includes('Network Error')) {
      // 网络错误处理
      console.log('网络错误:', error);
      return GetErrorMsg('网络错误，请检查您的网络连接。');
    } else if (error.response) {
      // 后端返回的错误
      console.log('后端错误:', error.response.data);
      return GetErrorMsg(error.response.data.message || '未知错误');
    } else {
      // 其他类型的错误
      console.log('其他错误:', error);
      return GetErrorMsg('发生了一个未知错误，请稍后再试。');
    }
  });
  /*
  return axios
    .get(apiTalk + url, realParams)
    .then((res) => {
      //if(loadingTime){clearTimeout(loadingTime);loadingTime=null;}
      if (res.data.result == "PwdError") {
        Toast.fail("密码错误,请重新登录")
      }
      return res.data
    })
    .catch((error) => {
      console.log(error)

      //if(loadingTime){ clearTimeout(loadingTime);loadingTime=null;}
      return GetErrorMsg(error)
    })*/
}
export const webApiPost = (
  url,
  params,
  showLoading = false,
  timeOutSetting = 300,
  useCoolieUri = false,
  apiTalkUri = ''
) => {
  for (var k in params) {
    if (typeof params[k] == "string") params[k] = params[k].trim().replace(/'/g, '');
  }
  if (showLoading) {
    loadingTime = setTimeout(() => {
      Toast.clear()
      Toast.loading({
        message: "请稍候...",
        forbidClick: true,
        duration: 5000,
      })
    }, timeOutSetting)
  }

  var destUri = apiTalk + url
  if (useCoolieUri ){
     if(!globalVars.coolieUri ) globalVars.coolieUri="https://coolies.yingjiang.co/"
     destUri = globalVars.coolieUri + url
  }
  else if(apiTalkUri && NODE_ENV === 'production') {
     destUri = apiTalkUri + '/' + url
  }


  return axios
    .post(destUri, { ...params })
    .then((res) => {
      if (showLoading) {
        Toast.clear()
      }
      return res.data
    })
    .catch((error) => {
      if (showLoading) {
        Toast.clear()
      }
      return GetErrorMsg(error)
    })
}
export const webApiFormPost = (
  url,
  params,
  showLoading = false,
  timeOutSetting = 300,
  skipInterceptor = false
) => {
  if (showLoading) {
    loadingTime = setTimeout(() => {
      Toast.clear()
      Toast.loading({
        message: "加载中...",
        forbidClick: true,
        duration: 5000,
      })
    }, timeOutSetting)
  }
  let formData = new FormData()
  for (var key in params) {
    formData.append(key, params[key])
  }
  return axios
    .post(apiTalk + url, formData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      skipInterceptor: skipInterceptor,
      timeout: 180 * 1000
    })
    .then((res) => {
      return res.data
    })
    .catch((error) => {
      return GetErrorMsg(error)
    })
}




// 添加响应拦截器 不用拦截器了
/*axios.interceptors.response.use(
  function(response) {
    if (response.config.skipInterceptor) {
      return response; // 直接返回响应，不执行拦截器的逻辑
    }
    Toast.clear()
    if (loadingTime) {
      clearTimeout(loadingTime)
      loadingTime = null
    }
    return response
  },
  function(error) {
    if (loadingTime) {
      clearTimeout(loadingTime)
      loadingTime = null
    }
    if (!(error.response && error.response.status == 404)) {
      if (error.toString().indexOf("Network Error")>=0) {
        Toast.clear()
        Toast("网络连接有问题")
      }
      else {
        Toast.clear()
        Toast("操作未能成功,请联系客服~")
      }

    }
    // console.log('错误提示'+error)
    return Promise.reject(error)
  }
)
*/


export const TransformToGaodePositionRequest = (params) => {
  return axios
    .get(`https://api.map.baidu.com/geoconv/v1/`, { params })
    .then((res) => res.data)
    .catch((error) => GetErrorMsg(error))
}
export const GetAddressByPoi = (params) => {
  return axios
    .get(`http://api.tianditu.gov.cn/geocoder`, { params })
    .then((res) => res.data)
    .catch((error) => GetErrorMsg(error))
}

export const GetUserMenus = (params) => {
  return webApiGet_retry("AppApi/Login/GetUserMenus", params)
}
export const GetUserCompanies = (params) => {
  return webApiGet_retry("AppApi/Login/GetUserCompanies", params)
}
export const GetHeartBeat = (params) => {
  return webApiGet_retry("AppApi/Login/HeartBeat", params)
}
export const Login = (params) => {
  return webApiPost_retry("AppApi/Login/Login", params)
}
export const GetInfoRegionName = (params) => {
  return webApiGet_retry("AppApi/Login/GetInfoRegionName", params)
}
export const ApiGetOperRights = (params) => {
  return webApiPost_retry("AppApi/Login/GetOperRights", params)
}
export const ApiReportLog = (params) => {
  return webApiPost_retry("AppApi/Login/ReportLog", params)
}
export const SendResetPwdSms = (params) => {
  return webApiPost_retry("AppApi/Login/SendResetPwdSms", params)
}
export const ForgetResetPwd = (params) => {
  return webApiPost_retry("AppApi/Login/ForgetResetPwd", params)
}

/*******************************************首页-统计分析****************************************************/
//  拜访门店
export const GetNewSupcustCount = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetNewSupcustCount", params)
}
export const GetYearSupcustCountGroupBySeller = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetYearSupcustCountGroupBySeller", params)
}

export const CancelVisit = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/RemoveVisitRecord", params)
}
export const ApiLoadVisitInfo = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/LoadVisitInfo", params)
}
export const ApiGetDisplayAgreementInfo = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetDisplayAgreementInfo", params)
}
export const SaveSupcustRemark = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveSupcustRemark", params)
}
export const ApiGetVisitInfo = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetVisitInfo", params)
}
export const CancelThumb = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/CancelThumb", params)
}
export const SaveThumb = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveThumb", params)
}
export const SaveComment = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveComment", params)
}
export const LoadVisitSchedule = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/LoadVisitSchedule", params)
}
export const LoadVisitDayList = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/LoadVisitDayList", params)
}
export const LoadThePlanSupcusts = (params) => {
  return webApiGet_retry("AppApi/VisitStrategy/GetVisitScheduleForSeller", params)
}
export const LoadTheDaySupcusts = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/LoadTheDaySupcusts", params)
}
export const InsertTheDaySupcust = (params) => {
  return webApiPost_retry("AppApi/VisitDay/InsertTheDaySupcust", params)
}
export const RemoveTheDaySupcust = (params) => {
  return webApiPost_retry("AppApi/VisitDay/RemoveTheDaySupcust", params)
}
export const SaveOrUpdateVisitDay = (params) => {
  return webApiPost_retry("AppApi/VisitDay/SaveOrUpdateVisitDay", params)
}
export const VisitDayUpdateOrderIndex = (params) => {
  return webApiPost_retry("AppApi/VisitDay/UpdateOrderIndex", params)
}
export const VisitDayGetAllSupcusts = (params) => {
  return webApiGet_retry("api/VisitDayEdit/GetSupcust", params)
}
export const GetDistributionSupcusts = (params) => {
  return webApiGet_retry("api/SupcustDistribution/GetQueryRecords", params)
}
export const GetDistributionSupcustInfo = (params) => {
  return webApiGet_retry("api/SupcustDistribution/GetSupcustInfo", params)
}
export const SaveScore = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveScore", params)
}
export const UpdateDisplayMaintainActionReview = (params) => {
  return webApiPost_retry(
    "AppApi/SheetVisit/UpdateDisplayMaintainActionReview",
    params
  )
}
export const UpdateDisplayMaintainForMonthActionReview = (params) => {
  return webApiPost_retry(
    "AppApi/SheetVisit/UpdateDisplayMaintainForMonthActionReview",
    params
  )
}
export const UpdateDisplayMaintainWorkContent = (params) => {
  return
  webApiPost_retry(
    "AppApi/SheetVisit/UpdateDisplayMaintainWorkContent",
    params
  )
}
export const UpdateDisplayMaintainWorkContentForMonth = (params) => {
  return webApiPost_retry(
    "AppApi/SheetVisit/UpdateDisplayMaintainWorkContentForMonth",
    params
  )
}
export const UpdateDisplayKeepActionReview = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/UpdateDisplayKeepActionReview", params)
}
export const UpdateDisplayKeepWorkContent = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/UpdateDisplayKeepWorkContent", params)
}
//访店汇总
export const GetVisitSummaryBySeller = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetVisitSummaryBySeller", params)
}
export const GetSupcustSummary = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetSupcustSummary", params)
}
//访店查询订单
// export const GetVisitSheetByVisitId=params=>{ return webApiGet_retry("AppApi/SheetVisit/SearchVisitSheetByVisitId",params) }

// 照片上传
/** 由于1201也有，为防止合并冲突以此命名 */
export const SaveSingleImage = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/SaveSingleImage", params,{showLoading:true})
}

// 获取仓库列表
export const GetBranchList = (params) => {
  return webApiGet_retry("AppApi/InfoBranch/GetBranchList", params)
}
//按照登录账号的仓库权限获取仓库列表
export const GetBranchListWithPermission = (params) => {
  return webApiGet_retry("AppApi/InfoBranch/GetBranchList", params)
}
//商品档案
export const GetItemInfoByBarCode = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemInfoByBarCode", params)
}
export const GetPicturesByItemWorld = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetPicturesByItemWorld", params)
}
export const GetGoodsItemList = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemList", params)
}
export const GetGoodsItemStock = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemStock", params)
}
export const GetItemInfoDetail = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemInfoDetail", params)
}
export const GetUnitnoList = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetUnitnoList", params)
}
export const SaveItemInfo = (params) => {
  return webApiPost_retry("AppApi/InfoItem/SaveItemInfo", params, {showLoading:true})
}
export const DeleteRecords = (params) => {
  return webApiPost_retry("AppApi/InfoItem/DeleteRecords", params)
}
export const CreateAttrName = (params) => {
  return webApiPost_retry("AppApi/InfoItem/CreateAttrName", params)
}
export const GetAttrItemInfos = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetAttrItemInfos", params)
}
export const SaveClass = (params) => {
  return webApiPost_retry("api/ClassEdit/Save", params)
}
export const DeleteClass = (params) => {
  return webApiPost_retry("api/ItemsView/RemoveClass", params)
}
export const SaveBrand = (params) => {
  return webApiPost_retry("api/BrandEdit/Save", params)
}
export const DeleteBrand = (params) => {
  return webApiPost_retry("api/BrandsView/DeleteRecords", params)
}
//新客雷达
export const GetSupcustRadarTypes = (params) => {
  return webApiGet_retry("AppApi/SupcustRadar/GetSupcustTypes", params)
}
export const RadarSearch = (params) => {
  return webApiPost_retry("AppApi/SupcustRadar/RadarSearch", params)
}
export const RadarLink = (params) => {
  return webApiPost_retry("AppApi/SupcustRadar/RadarLink", params)
}
//postStr={"keyWord":"北京大学","level":12,"mapBound":"116.02524,39.83833,116.65592,39.99185","queryType":1,"start":0,"count":9999}&type=query&tk=70dc5d689d0d92790715bcc5076778d6
export const RadarSearchByTDT = (params) => {
  return axios.get("https://api.tianditu.gov.cn/v2/search", { params })
}
// 客户档案
export const GetClientList = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetClientList", params)
}
export const GetClientById = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetClientById", params)
}
export const GetMaxSupcustNum = (params) => {
  return webApiPost_retry("AppApi/InfoClient/GetMaxSupcustNum", params)
}
export const GetGroup = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetGroup", params)
}
export const GetRank = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetRank", params)
}
export const SaveSupcustsInfo = (params) => {
  return webApiPost_retry("AppApi/InfoClient/SaveSupcustsInfo", params)
}
export const CheckData = (params) => {
  return webApiPost_retry("AppApi/InfoClient/checkData", params)
}
export const DeleteSupcustRecord = (params) => {
  return webApiPost_retry("AppApi/InfoClient/DeleteSupcustRecord", params)
}
export const SaveRegion = (params) => {
  return webApiPost_retry("api/RegionEdit/Save", params)
}
export const DeleteRegion = (params) => {
  return webApiPost_retry("api/ClientsView/RemoveRegion", params)
}
export const SaveGroup = (params) => {
  return webApiPost_retry("api/GroupEdit/Save", params)
}
export const DeleteGroup = (params) => {
  return webApiPost_retry("api/GroupsView/DeleteRecords", params)
}
export const SaveRank = (params) => {
  return webApiPost_retry("api/RankEdit/Save", params)
}
export const DeleteRank = (params) => {
  return webApiPost_retry("api/RanksView/DeleteRecords", params)
}
//供应商档案
export const SaveSuppliersInfo = (params) => {
  return webApiPost_retry("AppApi/InfoClient/SaveSuppliersInfo", params)
}
export const GetSupplierById = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetSupplierById", params)
}

// 获取地址列表
export const GetRegion = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetRegion", params)
}
export const ApiSubmitVisitStart = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SubmitVisitStart", params)
}
export const ApiSubmitVisitEnd = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SubmitVisitEnd", params)
}
// 应收款
export const GetArrears = (params) => {
  return webApiGet_retry("AppApi/SheetAcount/GetArrears", params)
}

// 预收款余额
export const GetPrepay = (params) => {
  return webApiGet_retry("AppApi/Report/GetPrepay", params)
}

//客户流失预警
export const GetRealTime = (params) => {
  return webApiGet_retry("AppApi/Report/GetRealTime", params)
}

//客户往来账
export const GetAccountHistory = (params) => {
  return webApiGet_retry("AppApi/Report/GetAccountHistory", params)
}

export const GetOptionsForSelect = (params) => {
  return webApiGet_retry("AppApi/SelectOptionsProvider/GetOptionsForSelect", params)
}

export const GetTreesForSelect = (params) => {
  return webApiGet_retry("AppApi/SelectTreesProvider/GetTreesForSelect", params)
}
// 获取员工列表
export const GetOperators = (params) => {
  return webApiGet_retry("AppApi/InfoOperator/GetOperators", params)
}
// 获取业务员列表
export const GetSellers = (params) => {
  return webApiGet_retry("AppApi/InfoOperator/GetSellers", params)
}
// 获取业务员列表（外勤轨迹）
export const GetSellersInTrail = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetSellers", params)
}
// 获取送货员列表
export const GetSenders = (params) => {
  return webApiGet_retry("AppApi/InfoOperator/GetSenders", params)
}
// 获取业务员列表
export const GetSellersWithOperRight = (params) => {
  return webApiGet_retry("AppApi/InfoOperator/GetSellersWithOperRight", params)
}
// 获取送货员列表
export const GetSendersWithOperRight = (params) => {
  return webApiGet_retry("AppApi/InfoOperator/GetSendersWithOperRight", params)
}
// 获取品牌列表
export const GetBrandList = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetBrandList", params)
}

// 调拨汇总表
export const GetMoveList = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/SheetMove/MoveList", params)
}
// 销售汇总
export const GetSalesSummary = (params) => {
  params.api_timeout=40000
  return webApiPost_retry("api/SalesSummaryByClientAndItem/GetQueryRecords", params)
}
// 商品类别
export const GetItemClass = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemClass", params)
}
//商品类别（包含品牌信息）
export const GetItemClassBrand = (params) => {
  return webApiGet_retry("AppApi/InfoItem/GetItemClassBrand", params)
}
// 销量走势图
export const GetTrends = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/Report/GetTrends", params)
}
// 业务销量排行
export const GetSellerRank = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/Report/GetSellerRank", params)
}
// 品牌销量排行
export const GetAmountByBrand = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/Report/GetAmountByBrand", params)
}
//热销商品排行
export const GetAmountByProduct = (params) => {
  params.api_timeout=60000
  return webApiGet_retry("AppApi/Report/GetAmountByProduct", params)
}
//热销商品排行
export const GetSaleOrderAmountByProduct = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/Report/GetSaleOrderAmountByProduct", params,{showLoading:true})
}
// 客户销量汇总
export const GetClientSaleSum = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("AppApi/Report/GetClientSaleSum", params,{showLoading:true})
}
// 经营利润表
export const GetBusinessProfit = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("api/BusinessProfit/GetSubjectBanlance", params,{showLoading:true})
}
//现金收支表
export const GetCashInOut = (params) => {
  params.api_timeout=40000
  return webApiGet_retry("api/CashInOut/GetSubjectBanlance", params,{showLoading:true})
}
// 库存查询
export const GetStockReport = (params) => {
  return webApiGet_retry("AppApi/Report/GetStockReport", params,{showLoading:true})
}
// 商品库存查询
export const GetStockItem = (params) => {
  return webApiGet_retry("AppApi/StockItem/GetStockItem", params,{showLoading:true})
}
// 商品库存变化明细查询
export const GetStockChangeDetail = (params) => {
  return webApiGet_retry("api/StocksChangeByOrder/GetQueryRecords", params,{showLoading:true})
}
// 商品库存变化汇总查询
export const GetStockChangeSummary = (params) => {
  return webApiGet_retry("api/StockChangeSum/GetQueryRecords", params,{showLoading:true})
}
// 商品库存变化锁定明细查询
export const GetStockLockChangeDetail = (params) => {
  return webApiGet_retry("AppApi/StockItem/GetStockLockChangeDetail", params,{showLoading:true})
}
// 收款对账
export const GetCheckSheetList = (params) => {
  return webApiGet_retry("AppApi/SheetCheckAcount/GetCheckSheetList", params,{showLoading:true})
}
export const NewCheckSheet = (params) => {
  return webApiGet_retry(
    "AppApi/SheetCheckAcount/NewCheckSheet",
    params,
    {showLoading:true,overlay:true}
  )
}
//return webApiGet_retry("AppApi/SheetCheckAcount/NewCheckSheet", params)};
export const SubmitCheckAccount = (params) => {
  return webApiPost_retry("AppApi/SheetCheckAcount/SubmitCheckAccount", params)
}
// 历史交账记录
export const LoadAccountHistory = (params) => {
  return webApiGet_retry("AppApi/SheetCheckAcount/LoadAccountHistory", params,{showLoading:true})
}
export const LoadCheckSheet = (params) => {
  return webApiGet_retry("AppApi/SheetCheckAcount/LoadCheckSheet", params)
}
// 预收款单
export const ApiGetPrepaySubjects = (params) => {
  return webApiGet_retry("AppApi/AppSheetPrepay/GetPrepaySubjects", params)
}
export const GetPayway = (params) => {
  return webApiGet_retry("AppApi/SheetAcount/GetPayway", params)
}
export const SubmitPrepay = (params) => {
  return webApiPost_retry("AppApi/AppSheetPrepay/Submit", params)
}
export const SavePrepay = (params) => {
  return webApiPost_retry("AppApi/AppSheetPrepay/Save", params)
}
export const AppSheetPrepayLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetPrepay/Load", params)
}
// 预付款
export const GetSubNamePay = (params) => {
  return webApiGet_retry("AppApi/SheetAcount/GetSubNamePay", params)
}

// 费用支出
export const GetFeeOut = (params) => {
  return webApiGet_retry("AppApi/AppSheetFeeOut/Load", params)
}
export const AppSheetFeeOutSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetFeeOut/Save", params)
}
export const AppSheetFeeOutSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetFeeOut/Submit", params)
}
export const AppSheetFeeOutLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetFeeOut/Load", params)
}
export const AppGetFeeOutDisplaySheetInfo = (params) => {
  return webApiPost_retry("AppApi/AppSheetFeeOut/GetFeeOutDisplaySheetInfo", params)
}

//其他收入
export const OtherInComeLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetOtherInCome/Load", params)
}
export const OtherInComeSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetOtherInCome/Save", params)
}
export const OtherInComeSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetOtherInCome/Submit", params)
}
export const OtherInComeRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetOtherInCome/Red", params)
}
export const OtherInComeDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetOtherInCome/Delete", params)
}

//获取用户自定义桌面
export const GetCustomDesk = (params) => {
  return webApiGet_retry("AppApi/AppDesk/GetCustomDesk", params)
}
//保存获取用户自定义桌面
export const SaveGetCustomDesk = (params) => {
  return webApiPost_retry("/AppApi/AppDesk/Save", params)
}
//部门长查看今日新增客户数和今日拜访数接口
export const DepartManagerGetVisitorCountAndCreateCustomerCount = (params) => {
  return webApiGet_retry("AppApi/AppDesk/DepartManagerGetVisitorCountAndCreateCustomerCount",params)
}
//业务员查看今日新增客户数和今日拜访数接口
export const SellerGetVisitorCountAndCreateCustomerCount = (params) => {
  return webApiGet_retry("AppApi/AppDesk/SellerGetVisitorCountAndCreateCustomerCount",params)
}
//老板端查看今日新增客户数和今日拜访数接口
export const BossGetVisitorCountAndCreateCustomerCount = (params) => {
  return webApiGet_retry("AppApi/AppDesk/BossGetVisitorCountAndCreateCustomerCount",params)
}

//销售单列表
//export const GetSheetRows = params => { return webApiGet_retry("AppApi/SheetSale/GetSheetRows", params)}
//销售单初始化
export const AppSheetSaleLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/Load", params)
}
//保存销售单
export const AppSheetSaleSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/Submit", params)
}
export const AppSheetSaleSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/Save", params)
}
export const AppSheetGetPrintInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetPrintInfo", params)
}
export const AppSheetGetPriceInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetPriceInfo", params)
}
//查预收款单
export const GetPrepaySheetForSub = (params) => {
  return webApiPost_retry("api/SaleSheet/GetPrepayDetail", params)
}
export const AppSheetBorrowItemLoad = (params) => {
  return webApiGet("AppApi/AppSheetBorrowItem/Load", params)
}
export const AppSheetBorrowItemSubmit = (params) => {
  return webApiPost("AppApi/AppSheetBorrowItem/Submit", params)
}
export const AppSheetBorrowItemSave = (params) => {
  return webApiPost("AppApi/AppSheetBorrowItem/Save", params)
}
export const ApiSheetBorrowItemReview = (params) => {
  return webApiPost("api/BorrowItemSheet/Review", params)
}
/** 获取支付系统的二维码 */
export const GetBillPayCode = (params) => {
  return webApiPost_retry("AppApi/PayBill/GetPayCode", params,{showLoading:true,msBeforeShowLoading:500})
}
/** 通过单据号获取关联订单的状态 */
export const GetBillStatus = (params) => {
  return webApiPost_retry("AppApi/PayBill/GetBillStatus", params,{showLoading:true,msBeforeShowLoading:500})
}
/** 申请订单退款 */
export const RefundBill = (params) => {
  return webApiPost_retry("AppApi/PayBill/RefundBill", params,{showLoading:true,msBeforeShowLoading:500})
}
/** 对账界面:获取基础信息 */
export const PayCheckGetBaseInfo = (params) => {
  return webApiPost_retry("AppApi/PayBill/LoadBaseInfo", params)
}
/** 对账界面:查询对账 */
export const PayCheckTrades = (params) => {
  return webApiPost_retry("AppApi/PayBill/CheckTrades", params,{showLoading:true,msBeforeShowLoading:500})
}

//加载商品列表
export const AppSheetSaleGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetItemList", params,{showLoading:true,msBeforeShowLoading:500})
}

// 加载促销活动
export const AppSaleGetPromotionList = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetSalePromotions", params)
}
// 加载促销商品详情
export const AppGetPromotionItemDetail = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/PostPromotionItemsDetail",params,{showLoading:true,msBeforeShowLoading:500})
}

//退货单
//export const AppSheetReturnLoad = params => { return webApiGet_retry("AppApi/AppSheetReturn/Load", params)}
// 提交退货单
//export const AppSheetReturnSubmit = params => { return webApiPost_retry("AppApi/AppSheetReturn/Submit", params) }

//采购单列表
export const AppGetBuySheets = (params) => {
  return webApiGet_retry("AppApi/SheetBuy/GetBuySheets", params)
}
//采购单初始化
export const AppSheetBuyLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetBuy/Load", params)
}
//保存采购单
export const AppSheetBuySave = (params) => {
  return webApiPost_retry("AppApi/AppSheetBuy/Save", params)
}

export const AppSheetBuySubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetBuy/Submit", params)
}
// 采购单红冲
export const SheetBuyRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetBuy/Red", params)
}
//加载商品列表
export const AppSheetBuyGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetBuy/GetItemList", params)
}

//获取业务员实时位置
export const GetSellerRealPosition = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetSellerRealPosition", params)
}
//外勤轨迹
export const GetTrail = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetTrail", params)
}
//加载调拨单
export const AppSheetMoveLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetMove/Load", params)
}

//合计提成
export const GetQueryRecords = (params) => {
  return webApiGet_retry("api/CommissionSummary/GetQueryRecords", params)
}
//提成查看限制
//export const GetOperInfo=()=>{
 //   return webApiGet_retry("api/CommissionSummary/GetOperInfo")
 // }

//提成明细
export const CommissionDetail = (params) => {
  return webApiGet_retry("api/CommissionDetail/GetQueryRecords", params)
}

//按销补货
export const AppSheetMoveGetFillItemsFromSale = (params) => {
  return webApiGet_retry("AppApi/AppSheetMove/GetFillItemsFromSale", params)
}
//按库存返库
export const AppSheetMoveGetFillItemsFromStock = (params) => {
  return webApiGet_retry("AppApi/AppSheetMove/GetFillItemsFromStock", params)
}

//交账后不允许开单
export const ApiCanApproveSheetToday = () => {
  return webApiGet_retry("AppApi/AppSheetSale/CanApproveSheetToday")
}
//依据库存预警限制装车调拨
export const ApiCheckVanStockOverLoad = (params) => {
  return webApiPost_retry("AppApi/AppSheetMove/CheckVanStockOverLoad",params)
}
//打印单据后不允许红冲/冲改单据
export const ApiNoRedSheetAfterPrint = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/NoRedSheetAfterPrint",params)
}
export const ApiNoRedOrderSheetAfterPrint = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/NoRedOrderSheetAfterPrint",params)
}
//加载调拨单商品
export const AppSheetMoveGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetMove/GetItemList", params)
}
//提交调拨单商品
export const AppSheetMoveSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetMove/Submit", params)
}
export const ApiSheetMoveSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetMove/Save", params)
}
//获取出仓商品库存
export const GetFromBranchItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetMove/GetFromBranchItemList", params)
}

export const AppSheetSaleOrderLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetSaleOrder/Load", params)
}
export const AppSheetSaleOrderGetItemStockQtys = (params) => {
  return webApiGet_retry("AppApi/AppSheetSaleOrder/GetItemStockQtys", params)
}
// 提交销售订单
export const AppSheetSaleOrderSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/Save", params)
}

export const ApiSheetSaleOrderApprove = (params) => {
  return webApiPost_retry("api/SaleOrderSheet/SaveAndApprove", params)
}
export const ApiSheetSaleOrderReview = (params) => {
  return webApiPost_retry("api/SaleOrderSheet/Review", params)
}

export const ApiSheetSaleReview = (params) => {
  return webApiPost_retry("api/SaleSheet/Review", params)
}
//加载特价审批单
export const AppSheetSpecialPriceLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetSpecialPrice/Load", params)
}
// 加载特价审批单商品
export const AppSheetSpecialPriceGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetSpecialPrice/GetItemList", params)
}
//审核特价审批单商品
export const AppSheetSpecialPriceSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetSpecialPrice/Submit", params)
}
//保存特价审批单
export const AppSheetSpecialPriceSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetSpecialPrice/Save", params)
}
//删除特价审批单
export const AppSheetSpecialPriceDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetSpecialPrice/Delete", params)
}
//红冲特价审批单
export const AppSheetSpecialPriceRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetSpecialPrice/Red", params)
}
// 加载盘点单
export const AppSheetInventoryLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetInventory/Load", params)
}
// 加载盘点单商品
export const AppSheetInventoryGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetInventory/GetItemList", params)
}
// 审核盘点单商品
export const AppSheetInventorySubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventory/Submit", params)
}
// 保存盘点单
export const AppSheetInventorySave = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventory/Save", params)
}
// 删除盘点单
export const AppSheetInventoryDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventory/Delete", params)
}
// 红冲盘点单
export const AppSheetInventoryRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventory/Red", params)
}
// 刷新盘点单商品
export const AppSheetInventoryGetStockQtyList = (params) => {
  return webApiFormPost_retry("api/InventorySheet/GetStockQtyList", params)
}

// 加载门店库存上报单
export const AppSheetStoreStockLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetStoreStock/Load", params)
}
// 加载门店库存上报单商品
export const AppSheetStoreStockGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetStoreStock/GetItemList", params)
}
// 审核门店库存上报单商品
export const AppSheetStoreStockSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetStoreStock/Submit", params)
}
// 保存门店库存上报单
export const AppSheetStoreStockSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetStoreStock/Save", params)
}
// 删除门店库存上报单
export const AppSheetStoreStockDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetStoreStock/Delete", params)
}
// 生成门店库存上报单
export const AppSheetStoreStockCreate = (params) => {
  return webApiPost_retry("AppApi/AppSheetStoreStock/CreateSaleSheet", params)
}
// 红冲门店库存上报单
export const AppSheetStoreStockRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetStoreStock/Red", params)
}
// 刷新门店库存上报单商品
export const AppSheetStoreStockGetStockQtyList = (params) => {
  return webApiFormPost_retry("api/StoreStockSheet/GetStockQtyList", params)
}

// 加载报损单GetItemList
export const AppSheetInventoryChangeLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetInventoryChange/Load", params)
}
// 审核报损单商品
export const AppSheetInventoryChangeSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventoryChange/Submit", params)
}
// 保存报损单
export const AppSheetInventoryChangeSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventoryChange/Save", params)
}
// 删除报损单
export const AppSheetInventoryChangeDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventoryChange/Delete", params)
}
// 红冲报损单
export const AppSheetInventoryChangeRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventoryChange/Red", params)
}

// 快捷报损
export const InventoryChangeGetFillItemsFromSale = (params) => {
  return webApiPost_retry(
    "AppApi/AppSheetInventoryChange/GetFillItemsFromSale",
    params
  )
}

export const AppSheetInventoryChangeGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetInventoryChange/GetItemList", params)
}

// 打开收款单
export const AppSheetArrearsLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetGetArrears/Load", params)
}
// 查询收款单
export const GetSalesSheets = (params) => {
  return webApiGet_retry("AppApi/AppSheetGetArrears/GetSaleSheets", params)
}
// 保存收款单
export const AppSheetArrearsSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetGetArrears/Save", params)
}
// 审核收款单
export const AppSheetArrearsSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetGetArrears/Submit", params)
}

// 查看单据
export const GetAllSaleOrderSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllSaleOrderSheets", params)
}
export const GetAllSaleSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllSaleSheets", params)
}
export const GetAllReturnOrderSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllReturnOrderSheets", params)
}
export const GetAllReturnSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllReturnSheets", params)
}
export const GetAllMoveSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllMoveSheets", params)
}
export const GetAllInventSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllInventSheets", params)
}
export const GetAllInventReduceSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllInventReduceSheets", params)
}
export const GetAllArrearsSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllArrearsSheets", params)
}
export const GetAllPrepaySheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllPrepaySheets", params)
}
export const GetAllFeeOutSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllFeeOutSheets", params)
}
export const GetBuySheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetBuySheets", params)
}
export const GetAllOrderItemSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllOrderItemSheets", params)
}
export const GetAllOtherInComeSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllOtherInComeSheets", params)
}
export const GetAllDisplayAgreementSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllDisplayAgreementSheets", params)
}
export const GetAllSpecialPriceSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllSpecialPriceSheets", params)
}
export const GetAllStoreStockSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllStoreStockSheets", params)
}
export const GetAllBorrowedItemSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllBorrowedItemSheets", params)
}
export const GetAllBorrowedReturnSheets = (params) => {
  return webApiGet_retry("AppApi/SheetAllSheets/GetAllBorrowedReturnSheets", params)
}

//用户历史操作
export const LogUserAction = (params) => {
  return webApiPost_retry("AppApi/LogUserAction/LogUserAction", params)
}

//修改密码
export const UpdateOperPwd = (params) => {
  return webApiPost_retry("AppApi/InfoOperator/UpdateOperPwd", params)
}
//获取单位
export const GetItemUnit = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetItemUnit", params)
}
//运行轨迹保存
export const SaveSellerTrail = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveSellerTrail", params)
}

// 红冲单据
export const SheetSaleRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/Red", params)
}
export const SheetSaleOrderRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/Red", params)
}
export const SheetMoveRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetMove/Red", params)
}
export const SheetInventoryRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetInventory/Red", params)
}
export const SheetGetArrearsRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetGetArrears/Red", params)
}
export const SheetFeeOutRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetFeeOut/Red", params)
}
export const SheetPrepayRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetPrepay/Red", params)
}
export const SheetCheckRed = (params) => {
  return webApiPost_retry("AppApi/SheetCheckAcount/RedCheckAccount", params)
}
export const SheetOrderItemRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetOrderItem/Red", params)
}
export const ApiApendSheetBrief = (params) => {
  return webApiPost_retry("AppApi/AppSheetCommon/AppendSheetBrief", params)
}
export const SheetBorrowItemRed = (params) => {
  return webApiPost_retry("AppApi/AppSheetBorrowItem/Red", params)
}
// 删除单据
export const SheetSaleDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/Delete", params)
}
export const SheetBuyDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetBuy/Delete", params)
}
export const SheetSaleOrderDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/Delete", params)
}

export const SheetOrderItemDelete = params => { return webApiPost_retry("AppApi/AppSheetOrderItem/Delete", params) }

export const SheetMoveDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetMove/Delete", params)
}
//盘点单无 export const SheetInventoryDelete = params => { return webApiPost_retry("AppApi/AppSheetInventory/Delete", params) }
export const SheetGetArrearsDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetGetArrears/Delete", params)
}
export const SheetFeeOutDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetFeeOut/Delete", params)
}
export const SheetPrepayDelete = (params) => {
  return webApiPost_retry("AppApi/AppSheetPrepay/Delete", params)
}
//export const SheetCheckDelete = params => { return webApiPost_retry("AppApi/SheetCheckAcount/Delete", params) }

// .responseType('arraybuffer')
export const GetVisitDetailLists = (params) => {
  return webApiGet_retry("AppApi/SheetVisit/GetVisitDetailLists", params)
}
//注册
export const RegisterApi = (params) => {
  return webApiGet_retry("AppApi/Login/Signup_main", params)
}

export const GetItemOrderedSumByItem = (params) => {
  return webApiGet_retry("AppApi/AppSheetOrderItem/GetItemOrderedSumByItem", params)
}
//定货会初始化
export const AppSheetOrderItemLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetOrderItem/Load", params)
}
//加载商品列表
//export const AppSheetOrderItemGetItemList = params => { return webApiGet_retry("AppApi/AppSheetSale/GetItemList", params)};

export const AppSheetOrderItemGetItemList = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetItemList", params, {showLoading:true})
}
export const AppSheetOrderItemSubmit = (params) => {
  return webApiPost_retry("AppApi/AppSheetOrderItem/Submit", params)
}
export const AppSheetOrderItemSaleSave = (params) => {
  return webApiPost_retry("AppApi/AppSheetOrderItem/Save", params)
}

// 客户账户信息
export const SheetSaleGetClientAccountInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetClientAccountInfo", params)
}
export const ApiSheetBuyGetSupplierAccountInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetBuy/GetSupplierAccountInfo", params)
}

// 销售单、销售订单 GetSaleOrOrderSaleHistory
export const AppGetSaleOrOrderSaleHistory = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetSaleOrOrderSaleHistory", params)
}
//保存销售单
// export const AppSheetSaleSubmit = params => { return webApiPost_retry("AppApi/AppSheetSale/Submit", params)}
// export const AppSheetSaleSave = params => { return webApiPost_retry("AppApi/AppSheetSale/Save", params)}
// 送货签收
//即将废弃
export const GetOrdersByClient = (params) => {
  return webApiGet_retry("AppApi/InfoClient/GetOrdersByClient", params)
}
export const GetOrdersForGrab = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForGrab", params)
}
export const GetOrdersForGrab_done = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForGrab_done", params)
}
export const GrabSheet = (params) => {
  return webApiPost_retry("AppApi/AppOrderManage/GrabSheet", params)
}
export const GetOrdersForSale = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForSale", params)
}
export const GetOrdersForSale_done = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForSale_done", params)
}
//访销-查单
export const GetAllOrders = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetAllOrders", params, {showLoading:true})
}
//访销-审核
export const GetOrdersForApprove = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForApprove", params)
}
//访销-复核
export const GetOrdersForReview = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForReview", params)
}
//访销-打印
export const GetOrdersForPrint = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForPrint", params)
}
//访销-装车
export const GetOrdersForAssignVan = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForAssignVan", params)
}
//访销-换车信息
export const GetOrdersInfoToChangeVan = (params) => {
  return webApiPost_retry("api/OrderManageAssignVan/GetOrdersInfoToChangeVan", params)
}
//访销-换车
export const ChangeVan = (params) => {
  return webApiPost_retry("api/OrderManageAssignVan/ChangeVan", params)
}
//访销-装车
export const GetAssignVanForApprove = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetAssignVanForApprove", params)
}
//访销-回库
export const GetOrdersForBackBranch = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetOrdersForBackBranch", params)
}
export const ApiGetBackBranchSheetForApprove = (params) => {
  return webApiGet_retry("AppApi/AppOrderManage/GetBackBranchSheetForApprove", params)
}
//访销-回库操作-获取回库单信息
export const ApiLoadBackBranchSheet = (params) => {
  return webApiPost_retry("api/DialogBackBranch/LoadBackBranchSheet", params)
}
//访销-回库操作-加载回库库存不足商品的库存使用情况
export const GetUnderStockItemsSheetsInfo = (params) => {
  return webApiPost_retry("api/DialogBackBranch/GetUnderStockItemsSheetsInfo", params)
}
//访销-回库操作-获取需要回库的单据信息
export const ApiGetOrdersInfoToBackBranch = (params) => {
  return webApiPost_retry("api/DialogBackBranch/GetOrdersInfoToBackBranch", params)
}
//访销-回库操作-获取已回库单据的明细行信息
export const GetBackBranchRowInfo = (params) => {
  return webApiPost_retry("AppApi/AppOrderManage/GetBackBranchRowInfo", params)
}
//访销-回库操作-回库操作
export const BackBranch = (params) => {
  return webApiPost_retry("api/DialogBackBranch/BackBranch", params)
}
//访销-回库操作-回库操作
export const SaveOrApproveBackBranch = (params) => {
  return webApiPost_retry("api/DialogBackBranch/saveOrApproveBackBranch", params)
}
//访销-回库操作-删除回库
export const DeleteBackBranch = (params) => {
  return webApiPost_retry("api/DialogBackBranch/DeleteBackBranch", params)
}
//访销-回库操作-撤销回库
export const ApiCancelBackBranch = (params) => {
  return webApiPost_retry("api/DialogBackBranch/CancelBackBranch", params)
}
//访销-回库操作-撤销回库
export const RedlBackBranchSheet = (params) => {
  return webApiPost_retry("api/DialogBackBranch/RedBackBranchSheet", params)
}
//访销-装车操作-获取需要装车的单据信息
export const ApiGetOrdersInfoToAssignVan = (params) => {
  return webApiPost_retry("api/DialogAssignVan/GetOrdersInfoToAssignVan", params)
}
//访销-装车操作-装车
export const AssignVan = (params) => {
  return webApiPost_retry("api/DialogAssignVan/AssignVan", params)
}
//访销-装车操作-撤销装车
export const CancelAssignVan = (params) => {
  return webApiPost_retry("api/DialogAssignVan/CancelAssignVan", params)
}
//访销-装车操作-获取装车单里的单据信息
export const GetAssignedOrders = (params) => {
  return webApiPost_retry("api/DialogAssignVan/GetAssignVanOpSheet", params)
}
//访销-获取车辆库存
export const ApiGetVanStock = (params) => {
  return webApiPost_retry("api/DialogAssignVan/GetVanStockQty", params)
}
//
export const QueryDesktopData = (params) => {
  return webApiPost_retry("AppApi/DesktopDataQuerier/QueryData", params)
}
// 获取商品信息
export const BuySheetGetItemsInfo = (params) => {
  return webApiGet_retry("api/BuySheet/GetItemsInfo", params)
}
// 导入采购单图片
export const BuySheetImportItems = (params) => {
  return webApiFormPost_retry("api/BuySheet/HUAWEIOCRImportItems", params)
}
// 智能导入(GPT)
export const GptTableRecognition = (params) => {
  return webApiFormPost_retry("api/BuySheet/GptTableRecognition", params)
}
// 智能导入(Kimi)
export const KimiTableRecognition = (params) => {
  return webApiFormPost_retry("api/BuySheet/KimiTableRecognition", params,{timeout:90000})
}
// 获取导入的自定义表头
export const GetCustomImportHeader = (params) => {
  return webApiGet_retry("api/BuySheet/GetCustomHeader", params)
}
export const SaleSheetGetItemsInfo = (params) => {
  return webApiPost_retry("api/SaleSheet/GetItemsInfo_Post", params)
}
//订单拒收/恢复
export const SaleOrderSheetReject = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/Reject", params)
}
export const SaleOrderSheetRecover = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/Recover", params)
}

//回撤
export const SaleOrderSheetRetreat = (params) => {
  return webApiPost_retry("api/OrderManageOrderToSale/RetreatAssignVan", params)
}

//保存送货路线顺序
export const SaveDeliveryRouteOrder = (params) => {
  return webApiPost_retry("AppApi/AppOrderManage/SaveDeliveryRouteOrder", params)
}

//自定义桌面
export const GetDesktop = (params) => {
  return webApiGet_retry("AppApi/AppDesk/GetDesktop", params)
}

export const SaveDesktop = (params) => {
  return webApiPost_retry("AppApi/AppDesk/SaveDesktop", params)
}
//个性化设置
export const UpdateOperSetting = (params) => {
  return webApiPost_retry("AppApi/AppDesk/UpdateOperSetting", params)
}
//export const GetPersonalSetting = (params)=>{
// return webApiGet_retry("AppApi/AppDesk/GetUserPersonalSetting",params)
//}
// 追加备注
export const AppendBriefSheetX = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/AppendBrief", params)
}
export const AppendBriefSheetXD = (params) => {
  return webApiPost_retry("AppApi/AppSheetSaleOrder/AppendBrief", params)
}
export const AppendBriefSheetCG = (params) => {
  return webApiPost_retry("AppApi/AppSheetBuy/AppendBrief", params)
}

// 商品属性

export const GetItemsForAttrRows = (params) => {
  return webApiGet_retry("api/SaleSheet/GetItemsForAttrRows", params)
}
export const GetSpecialPriceItemsForAttrRows = (params) => {
  return webApiGet_retry("api/SaleSheet/GetSpecialPriceItemsForAttrRows", params)
}
export const CreateItemsForAttrRows = (params) => {
  return webApiPost_retry("api/SaleSheet/CreateItemsForAttrRows", params)
}
export const GetItemsForAttrRows_SpecialPrice = (params) => {
  return webApiGet_retry("AppApi/AppSheetSpecialPrice/GetItemsForAttrRows", params)
}
// 票证通
export const LoginPiaoZhengTong = (params) => {
  return webApiPost_retry("api/SaleSheet/LoginPiaoZhengTong", params)
}
export const UploadSheetToPiaoZhengTong = (params) => {
  return webApiPost_retry("api/SaleSheet/UploadSheetToPiaoZhengTong", params)
}
export const AddSupcustToPiaoZhengTong = (params) => {
  return webApiPost_retry("api/ClientEdit/SaveSupcustPiaoZhengTong", params)
}

//考勤
export const GetAttendanceGroup = (params) => {
  return webApiPost_retry("AppApi/Attendance/GetGroupInfo", params)
}
export const GetTodayAttendanceInfos = (params) => {
  return webApiPost_retry("AppApi/Attendance/GetTodayAttendanceInfos", params)
}
export const ApiGetMyGroupInfo = (params) => {
  return webApiPost_retry("AppApi/Attendance/GetMyGroupInfo", params)
}
export const SaveSign = (params) => {
  return webApiPost_retry("AppApi/Attendance/SaveSign", params)
}
export const GetAttendanceRecords = (params) => {
  return webApiGet_retry("api/AttenanceMonthReportView/GetQueryRecords", params)
}
export const AddAttenceLeave = (params) => {
  return webApiPost_retry("AppApi/AttenceLeave/Add", params)
}
export const GetMyApplys = (params) => {
  return webApiGet_retry("AppApi/AttenceLeave/GetMyApplys", params)
}
export const GetMyAudits = (params) => {
  return webApiGet_retry("AppApi/AttenceLeave/GetMyAudits", params)
}
export const AuditOK = (params) => {
  return webApiPost_retry("AppApi/AttenceLeave/AuditOK", params)
}
export const AuditReject = (params) => {
  return webApiPost_retry("AppApi/AttenceLeave/AuditReject", params)
}
export const ApplyRecall = (params) => {
  return webApiPost_retry("AppApi/AttenceLeave/ApplyRecall", params)
}
export const DeleteLeave = (params) => {
  return webApiPost_retry("AppApi/AttenceLeave/DeleteLeave", params)
}

//保存缓存轨迹
export const SaveTrailsAfterNetworkFail = (params) => {
  return webApiPost_retry("AppApi/SheetVisit/SaveTrailsAfterNetworkFail", params)
}

//云打印相关
export const ImportCloudPrinters = (params) => {
  return webApiGet_retry("api/PrintTemplate/GetCloudPrintersToUse", params)
}
export const BindCloudPrinter = (params) => {
  return webApiPost_retry("AppApi/CloudPrint/BindPrinter", params, {showLoading:true})
}
export const SaveCloudPrinter = (params) => {
  return webApiPost_retry("api/CloudPrinterEdit/Save", params)
}
//export const UnBindCloudPrinter = params => { return webApiPost_retry("WebApi/CloudPrint/UnBindPrinter",params) }
export const AppSheetToEsc = (params) => {
  return webApiPost_retry("AppApi/CloudPrint/SheetToEsc", params, {useCoolieUri:true})
}
export const AppCloudPrint_sheetTmp = (params) => {
  return webApiPost_retry("AppApi/CloudPrint/PrintSheetWithTemplate",params,{useCoolieUri:true})
}
export const AppCloudPrint_escCmd = (params) => {
  return webApiPost_retry("AppApi/CloudPrint/PrintSheetWithEscCmd",params,{useCoolieUri:true})
}
export const AppSheetToImages = (params) => {
  return webApiPost_retry("AppApi/CloudPrint/SheetToImages", params, {useCoolieUri:true})
}
export const AppGetSheetToPrint = (params) => {
  return webApiGet_retry("AppApi/CloudPrint/GetSheetToPrint", params)
}
export const AppGetTemplate = (params) => {
  return webApiGet_retry("api/PrintTemplate/GetTemplateToUse", params)
}

export const ApiPrintMark = (params) => {
  return webApiPost_retry("api/Printer/PrintMark", params)
}
// 微信相关
export const WeChatGetQrCodeTicket = (params) => {
  return webApiPost_retry("WeChat/GetQrCodeTicket", params)
}
// export const WeChatGetQrCodeImg= params => { return webApiPost_retry("WeChat/GetQrCodeImg",  params)}
// export const WeChatSendSheetSimple= params => { return webApiPost_retry("WeChat/SendSheetSimple",  params)}
// 微信小程序
export const WeXinMiniCreateMiniQRCode = (params) => {
  return webApiPost_retry("WeChat/CreateMiniQRCode", params)
}

// 陈列协议相关
export const DisplayAgreementLoad = (params) => {
  return webApiGet_retry("AppApi/AppSheetDisplayAgreement/Load", params)
}
export const DisplayGetFeeOutSubForKS = (params) => {
  return webApiGet_retry("AppApi/AppSheetDisplayAgreement/GetFeeOutSubForKS", params)
}
export const DisplaySaveAndApprove = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/SaveAndApprove", params)
}
export const DisplaySave = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/Save", params)
}
export const DisplayDelete = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/Delete", params)
}
export const GetCurrentDisplayList = (params) => {
  return webApiPost_retry("AppApi/AppSheetDisplayAgreement/GetCurrentDisplayList",params)
}

// 中止
export const DisplayAgreementSheetTerminate = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/Terminate", params)
}
export const DisplayAppendBrief = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/AppendBrief", params)
}
// 陈列协议模板相关
export const QueryAllInfoDisplayTemplate = (params) => {
  return webApiPost_retry("common/visitDisplay/QueryAllInfoDisplayTemplate", params)
}
export const QueryOneInfoDisplayTemplate = (params) => {
  return webApiPost_retry("common/visitDisplay/QueryOneInfoDisplayTemplate", params)
}
export const UpdateDisplaySignActionReview = (params) => {
  return webApiPost_retry("AppApi/AppSheetDisplayAgreement/UpdateDisplaySignActionReview",params)
}
export const UpdateDisplaySignWorkContent = (params) => {
  return webApiPost_retry("api/DisplayAgreementSheet/UpdateDisplaySignWorkContent",params)
}
// 兑付复核
export const UpdateDisplayGiveActionReview = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/UpdateDisplayGiveActionReview", params)
}
export const UpdateDisplayGiveActionWorkContent = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/UpdateDisplayGiveActionWorkContent",params)
}
export const QueryDisplayTemplateActionsForFdSender = (params) => {
  return webApiPost_retry("AppApi/AppSheetSale/QueryDisplayTemplateActionsForFdSender",params)
}

// 消息系统， 获取页面统计数据
export const QueryMessageTypeCount = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageTypeCount",params)
}
export const QueryMessageTodoList = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageTodoList",params)
}
export const QueryMessageProofPolishList = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageProofPolishList", params)
}
export const QueryMessageNoticeList = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageNoticeList",params)
}
export const CreateReadMessage = (params) => {
  return webApiPost_retry("yingjiangMessage/createMessage/CreateReadMessage", params)
}
export const QueryMessageSubscribeType = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageSubscribeType",params)
}
export const QueryGetVisitDetailInfo = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryGetVisitDetailInfo",params)
}
export const QueryMessageTotalCount = (params) => {
  return webApiPost_retry("yingjiangMessage/queryMessage/QueryMessageTotalCount",params)
}

// 商城相关
export const QueryMallSettingInfo = (params) => {
  return webApiPost_retry("MallApi/MallSetting/QueryMallSettingInfo", params)
}
export const CreateMiniQrCodeBase64 = (params) => {
  return webApiPost_retry("MallApi/MallQrCode/CreateMiniQrCodeBase64", params,{apiTalkUri:globalVars.miniQrCodeUri})
  return webApiPost("MallApi/MallQrCode/CreateMiniQrCodeBase64", params, false, 300, false, globalVars.miniQrCodeUri)

}

// 备注
export const GetBriefListForInfoSheetDetailBrief = (params) => {
  return webApiPost_retry(
    "AppApi/InfoBrief/GetBriefListForInfoSheetDetailBrief",
    params
  )
}

//销售明细
export const GetSaleDetails = (params) => {
  return webApiPost_retry("api/SalesDetail/GetQueryRecords", params)
}

// 付款单
export const AppSheetGetArrearsGetBuySheets = (params) => {
  return webApiGet_retry("AppApi/AppSheetGetArrears/GetBuySheets", params)
}

//付款单和收款单综合接口
export const AppSheetGetArrearsGetAllSheets = (params) => {
  return webApiGet_retry("AppApi/AppSheetGetArrears/GetALLsheets", params)
}

//获取仓库/库位
export const GetBranchsInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetBranchsInfo", params)
}

export const GetStockInfo = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/GetStockInfo", params)
}

export const GetBatchStock = (params) => {
  if(params.item_id.indexOf('nanoid')==0){
    return new Promise((resolve, reject) => {
      // 模拟异步操作，比如从服务器获取数据
      resolve({result:"OK",data:{batchStockTotal:[],batchStock:[]}})
    })
  }

  return webApiGet_retry("api/StockInOutSheet/GetItemBatchStock", params)
}
export const GetBranchPositionForReturn = (params) => {
  return webApiGet_retry("api/SaleSheet/GetBranchPositionForReturn", params)
}
export const GetBranchPositionForMove = (params) => {
  return webApiGet_retry("api/MoveSheet/GetBranchPositionForMove", params)
}
export const GetBatchStockForMove = (params) => {
  if(params.item_id.indexOf('nanoid')==0){
    return new Promise((resolve, reject) => {
      resolve({result:"OK",data:{fromBatchStock:[],toBatchStock:[], batchStock:[]}})
    })
  }
  return webApiGet_retry("api/MoveSheet/GetItemBatchStock", params)
}

export const  SearchSalePromotions = (params) => {
  return webApiGet_retry("AppApi/AppSheetSale/SearchSalePromotions", params)
}

//商城用户获取list
export const  GetInfoCustContactList = (params) => {
  return webApiPost_retry("MallMini/MallMiniInfoCustContact/GetInfoCustContactList", params)
}

export const  BulkUpdateInfoCustContactApproveStatus = (params) => {
  return webApiPost_retry("MallMini/MallMiniInfoCustContact/BulkUpdateInfoCustContactApproveStatus", params)
}

export const UnBindInfoCustContact = (params) => {
  return webApiPost_retry("MallMini/MallMiniInfoCustContact/UnBindInfoCustContact", params)
}


