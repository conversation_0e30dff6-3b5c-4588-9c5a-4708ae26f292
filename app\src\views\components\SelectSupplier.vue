<template>
  <div class="public_query">
    <div class="public_query_title">
      <van-form v-model="conQueryForm">
        <div :style="canHide?'padding-top:10px':''" class="public_query_titleSrc">
            <div v-if="canHide" style="padding:10px;0px;" @click="hide">
              <svg width="22px" height="22px">
                <use :xlink:href="'#icon-hide'"></use>
              </svg>
            </div>
          <div class="public_query_titleSrc_item">
            <input
               type="text"
               v-model="queryCondition.searchStr"
               placeholder="名称"
               @input="onClientNameInput"
            />
            <van-icon name="user-o" />
          </div>
         <!-- <div class="public_query_titleSrc_item">
            <input
              type="text"
              v-model="conQueryForm.regionName"
              placeholder="请选择片区"
              readonly
              @click="onAddressShow"
            />
            <van-icon name="wap-home-o" />
          </div>-->
        </div>
      </van-form>
      <div class="public_list_title">
        <div>名称</div>
        <div>联系电话</div>
              </div>
    </div>
    <div class="sales_list_boxs" v-if="clientList.length>0">
      <van-list
        v-model="loading"
        :finished="finished"
        finished-text="到底了~"
        @load="onNextPage"
      >
        <ul class="sales_ul">
          <li v-for="(item, index) in clientList" :key="index">
            <div class="sales_ul_t">
              <div class="sales_ul_tl" @click="onSelectActive(item)">{{ item.sup_name }}</div>
              <a v-if="!isiOS&&!isHarmony" class="sales_ul_tr" :href="'tel:'+ item.sup_tel">{{ item.sup_tel }}</a>
              <div v-else @click="call(item.sup_tel)">{{item.sup_tel}}</div>
            </div>
            <!-- <div class="sales_ul_b" @click="onSelectActive(item)"> -->
          <!--  <div class="sales_ul_b">
              <div class="sales_ul_bl" @click="onSelectActive(item)">{{ item.sup_addr }}</div>
              <div class="sales_ul_br" style="color:#666;size:0.5rem">

                <div v-if="item.distance==-1">
                   {{item.distanceStr}}
                 </div>
                  <div v-else @click="openNavigationAppDialog(item)">

                  {{item.distanceStr}}<i class="iconfont">&#xe640;</i>
                </div>
              </div>
            </div>-->
          </li>
        </ul>
      </van-list>
    </div>
    <div class="sales_list_boxs_no" v-if="clientList.length<=0">
      <div class="whole_box_no_icon iconfont">
        &#xe664;
      </div>
      <p>未查询到信息</p>
    </div>
    <van-popup style="overflow: hidden!important;" v-model="showAddress" duration="0.4" position="bottom" :style="{ height: '90%', width: '100%' }">
        <region-selection @isAddressHide="onRegionSelectionHide"></region-selection>
    </van-popup>
    <van-popup v-model="showChooseNavigationApp" position="bottom" :style="{ height: '30%' }" >
      <div class="navi-select-item" @click="onNaviSelectItem(item)" v-for="(item,index) in navigationAppList" :key="index">{{item.name}}</div>
    </van-popup>
    <div class="wrapper" v-show="!showAddress">
      <div class="content">共<span class="record">{{total}}</span>条</div>
    </div>
  </div>
</template>
<script>
import { Form, Icon, List,Popup} from "vant"
import { GetClientList } from "../../api/api"
import RegionSelection from "../components/RegionSelection"
import positon from '../Position/position'
export default {
  name: "SelectSupplier",
  props:{
    canHide:false
  },
  data() {
    return {
      conQueryForm: {
        clientName: "",
        regionName: "",
      },
      queryCondition:{
          supcustFlag:'',
          searchStr:'',
          regionID:'',
          currentLng:'',
          currentLat:''
      },
      coords:{},
      list: [],
      loading: false,
      finished: false,
      pageSize: 20,
      startRow: 0,
      getTotal: true,
      navigationAppList:[
        {
          id:1,
          name:"高德地图"
        },
                {
          id:2,
          name:"百度地图"
        }
      ],
      showChooseNavigationApp:false,
      clientList: [],
      showAddress:false,
      selectedSupcustNavigatorInfo:{},
      total: 0
    }
  },
  mounted() {
    this.newQuery()

  },
  computed:{
    isHarmony(){
      return window.isHarmony
    }
  },
  components: {
    "van-form": Form,
    "van-icon": Icon,
    "van-list": List,
    "van-popup":Popup,
    "region-selection":RegionSelection
  },
  methods: {
    hide(){
      this.$emit("handleHide")
    },
    call(tel) {
      var ref = window.open("tel://" + tel,"_system")
      if(isHarmony){
        ref.show()
      }
    },
    newQuery() {

      this.startRow = 0
      this.finished = false
      this.clientList = []

      this.startRow=0
      this.onNextPage()
    },
    openNavigationAppDialog(item){
      //打开弹出框并且把选中商家的导航信息存入全局变量
      this.selectedSupcustNavigatorInfo={
       sup_addr:item.sup_addr,
       addr_lng:item.addr_lng,
       addr_lat:item.addr_lat
      }
      this.showChooseNavigationApp=true
    },
    onNaviSelectItem(item){
      console.log(item)
      if (isiOS) {
        this.jumpiOSNaviUrlBySelectAppName(item.name);
      } else if(isHarmony){
        this.jumpHarmonyNaviUrlBySelectAppName(item.name);
      } else{
        this.jumpAndroidNaviUrlBySelectAppName(item.name);
      }
      //隐藏弹出框
      this.showChooseNavigationApp=false
    },
    async getCurPosition(){
        const positionService=new Position(isiOS)
        const position = await positionService.currentPosition()
        console.log(position)
        return position
    },
    async jumpHarmonyNaviUrlBySelectAppName(name) {
      //从全局变量中取选中商家的导航信息
      const navigationInfo = this.selectedSupcustNavigatorInfo;
      const { addr_lng, addr_lat, sup_addr } = navigationInfo
      const { longitude, latitude }=await this.getCurPosition()
      if (name == "百度地图") {
        const navi = new Navi("baidu", isiOS, addr_lng + "," + addr_lat, sup_addr, isHarmony)
        var ref = cordova.InAppBrowser.open(navi.getBaiduUrl(), "_system");
        ref.show();
      }
      if (name == "高德地图") {
        const navi = new Navi("gaode", isiOS,addr_lng + "," + addr_lat, sup_addr, isHarmony, longitude + "," + latitude)
        var ref = cordova.InAppBrowser.open(await navi.getGaoDeUrl(), "_system");
        ref.show();
      }
    },
    jumpiOSNaviUrlBySelectAppName(name){
        //从全局变量中取选中商家的导航信息
        const navigationInfo= this.selectedSupcustNavigatorInfo
        if (name=="百度地图") {
          window.open(this.generateiOSBaiduJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat),"_system")
        }
        if (name=="高德地图") {
            window.open(this.generateiOSGaoDeJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat),"_system")
        }
    },
    jumpAndroidNaviUrlBySelectAppName(name){
          //从全局变量中取选中商家的导航信息
          const navigationInfo= this.selectedSupcustNavigatorInfo
        if (name=="百度地图") {
          window.location.href=this.generateAndroidBaiduJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat)
        }
        if (name=="高德地图") {
          window.location.href=this.generateAndroidGaoDeJumpNavigationAppUrl(navigationInfo.sup_addr,navigationInfo.addr_lng,navigationInfo.addr_lat)
        }
    },
    generateiOSGaoDeJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateGaoDeJumpNavigationAppUrl("iosamap",supAddr,addr_lng,addr_lat)
      return  `${naviurl}&src=ios.baidu.openAPIdemo`
    },
    generateAndroidGaoDeJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateGaoDeJumpNavigationAppUrl("androidamap",supAddr,addr_lng,addr_lat)
      return naviurl
    },
    generateGaoDeJumpNavigationAppUrl(protocol,supAddr,addr_lng,addr_lat){
    const naviurl=`${protocol}://navi?sourceApplication=appname&poiname=${supAddr}&lat=${addr_lat}&lon=${addr_lng}&dev=1&style=2`
    return naviurl
    },
    generateiOSBaiduJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateBaiduJumpNavigationAppUrl("baidumap",supAddr,addr_lng,addr_lat)
      return  `${naviurl}&type=BLK&src=ios.baidu.openAPIdemo`
    },
    generateAndroidBaiduJumpNavigationAppUrl(supAddr,addr_lng,addr_lat){
      const naviurl=this.generateBaiduJumpNavigationAppUrl("bdapp",supAddr,addr_lng,addr_lat)
      return `${naviurl}&src=andr.baidu.openAPIdemo`
    },
    generateBaiduJumpNavigationAppUrl(protocol,supAddr,addr_lng,addr_lat){
    const naviurl=`${protocol}://map/navi?query=${supAddr}&coord_type=bd09ll&location=${addr_lat},${addr_lng}`
    return naviurl
    },

    getiOSdGpsPosition(cbSuccess,cbFail){

      var options = {
      enableHighAccuracy: true,  // 是否使用 GPS
      coorType: 'bd09ll',
      locationClientMode: 'realtime' // 添加：使用实时定位模式
      }
      if(window.baidumap_location){
        window.baidumap_location.getCurrentPosition(res=>{
              const longitude=res.longitude
              const latitude=res.latitude
              const currentPosition={
                  longitude,latitude
              }
              cbSuccess(currentPosition)
            },error=>{
               cbFail()
           })
      }
      else{
        cbFail()
      }

    },

    onNextPage() {
      if(this.finished) return
      let params = {
        ...this.queryCondition,
        supcustFlag:'S',
        pageSize: this.pageSize,
        startRow: this.startRow,
        getTotal: this.getTotal

      }
      GetClientList(params).then((res) => {
        console.log(res)
        if (res.result === "OK") {
          res.data.map(item=>{
            if(item.distance===''){
               const ILLEGAL_DISTANCE=-1
               item.distance=ILLEGAL_DISTANCE
               item.distanceStr="未知"
            }
            else{
                item.distanceStr= this.processDistanceAndFormatUnit(parseFloat(item.distance))
            }
            this.clientList.push(item)
            this.total = res.total
          })
          this.loading = false
          this.startRow = Number(this.startRow)+this.pageSize
          if (this.clientList.length >= Number(res.total)){
            this.finished = true
          }
        }
      })
    },
    isDistanceMoreOneKM(distance){
        return distance>1000;
    },
    processDistanceAndFormatUnit(distance){
       let distanceStr=''
       if(this.isDistanceMoreOneKM(distance)) {
          distance=distance/1000
          distanceStr=distance.toFixed(2)+" km "
       }
       else{
          distanceStr=parseInt(distance)+" m "
       }
       return distanceStr
    },
    onSelectActive(obj){
      let objs = {
        titles:obj.sup_name,
        ids:obj.supcust_id,
        isShow:false,
        mobile: obj.sup_tel,
        acct_type: obj.acct_type
      }
      this.$emit('onClientSelected',objs)
    },
    onAddressShow(){
      this.showAddress = true
    },
    onRegionSelectionHide(objs){
      this.showAddress = objs.isShow
      if(objs.itemObjs.titles) {
        this.conQueryForm.regionName = objs.itemObjs.titles
        this.queryCondition.regionID = objs.itemObjs.ids
      }else{
        this.conQueryForm.regionName = ''
        this.queryCondition.regionID = ''
      }
      this.newQuery()
    },
    onClientNameInput() {
      this.newQuery()
    }
  }
}
</script>
<style lang="less" scoped>
// height:136px
@flex_w: {
  display: flex;
  flex-wrap: wrap;
};
@flex_a_j: {
  display: flex;
  align-items: center;
  justify-content: center;
};
@flex_a_bw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_a_end: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column
};
.navi-select-item{
  font-size: 0.65rem;
  color: #1887f7;
  border-bottom: solid 0.025rem #ccc;
  padding-top: .5rem;
  padding-bottom: .5rem;
}
.public_query {
  background: #ffffff;
  height: 100%;
  .public_query_title {
    padding-top: 5px;
    border-bottom: 1px solid #f2f2f2;
    .public_query_titleSrc {
      padding: 0 10px;
      height: 35px;
      display: flex;
      flex-direction: row;
      margin-top: 5px;
      .public_query_titleSrc_item {
        width: 48%;
        height: 100%;
        margin-left: 20px;
        border: 1px solid #cccccc;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        vertical-align: top;
        // div {
        //   height: 100%;
        //   width: calc(100% - 40px);
        //   padding: 0 30px 0 10px;
        //   border: none;
        //   font-size: 15px;
        //   line-height: 35px;
        //   color: #333333;
        //   text-align: left;
        // }
        input {
          height: 100%;
          width: calc(100% - 45px);
          padding: 0 45px 0 5px;
          border: none;
          font-size: 15px;
          line-height: 35px;
          color: #333333;
          vertical-align: top;
        }
        span{
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 45px;
          font-size: 16px;
          color: #000;
          @flex_a_j();
          background: #4c99e7;
        }
        .van-icon {
          position: absolute;
          right: 0;
          top: 0;
          bottom: 0;
          width: 30px;
          font-size: 22px;
          @flex_a_j();
          color: #666666;
        }
      }
    }
    .public_list_title {
      height: 40px;
      @flex_a_bw();
      margin-top: 5px;
      padding: 0 5px;
      div {
        height: 40px;
        line-height: 40px;
        font-size: 15px;
        text-align: center;
        width: calc(25% - 10px);
        padding: 0 5px;
        font-weight: 500;
        color: #333333;
      }
      div:first-child {
        width: calc(50% - 10px);
        text-align: left;
      }
      div:last-child {
        width: calc(25% - 10px);
        text-align: right;
      }
    }
  }
}
.sales_list_boxs {
  height: calc(100% - 88px);
  overflow-x: hidden;
  overflow-y: auto;
  background: #f2f2f2;
}
.sales_list_boxs_no{
  height: calc(100% - 54px);
  @flex_a_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon{
    font-size: 50px;
  }
  p{
    font-size: 15px;
  }
}
.sales_ul {
  height: auto;
  overflow: hidden;
  padding: 0 5px;
  background: #ffffff;
  li {
    height: auto;
    overflow: hidden;
    padding: 10px 5px;
    border-bottom: 1px solid #f2f2f2;
    .sales_ul_t {
      overflow: hidden;
      @flex_a_bw();
      height: auto;
      div {
        font-size: 15px;
      }
      .sales_ul_tl {
        color: #333333;
        width: 73%;
        text-align: left;
      }
      .sales_ul_tr {
        font-size: 15px;
        color: #1989fa;
        width: 27%;
        text-align: right;
      }
    }
    .sales_ul_b {
      overflow: hidden;
      @flex_a_bw();
      height: auto;
      margin-top: 5px;
      .sales_ul_bl {
        font-size: 13px;
        color: #666666;
        width: 66%;
        min-height:15px;
        text-align: left;
      }
      .sales_ul_br {
        font-size: 13px;
        width: 32%;
        color: #666666;
        height: 30px;
        @flex_a_end();
        i {
          font-size: 22px;
          color: #1989fa;
          margin-left: 10px;
        }
      }
    }
  }
  li:last-child {
    border-bottom: none;
  }
}
	.wrapper{
      position: fixed;
      left: 0px;
      bottom: 0px;
      width: 100%;
      height: 35px;
      font-size: 0.5em;
      color: #555;
      background-color: #f4f4f4;
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: flex-end;
        .content{
          padding-right: 15px;
        }
        .record{
          padding: 0 10px;
        }
    }
</style>

