<template>
  <div class="pages">
    <van-nav-bar title="应收款" left-arrow @click-left="myGoBack($router)" safe-area-inset-top>
      <!-- <template #right>
        <van-icon name="clock-o" size="22" @click="showCalendar = true" />
      </template> -->
    </van-nav-bar>
    <div class="public_box2">
      <div class="public_box2_t">
        <van-form v-model="formData" style="display:flex;flex-wrap: wrap;">
          <van-search class="van_search" v-model="formData.name" left-icon="" right-icon="search" placeholder="客户" @input="searchKeyInput" />
          <div class="van_search">
            <YjSelectTree
              ref="selectTreeRef"
              :target="target"
              :title="title"
              :confirmColor="confirmColor"
              :rangeKey="rangeKey"
              :rootNode="rootNode"
              :idKey="idKey"
              :showClearBtn="showClearBtn"
              :sonNode="sonNode"
              :multipleCheck="multipleCheck"
              :parentSelectable="parentSelectable"
              :popupHeight="'90%'"
              @getRootNode="getRootNode"
              @handleConfirm="onRegionSelected"
            >
              <template #select-tree-content>
                <van-search class="van_search" style="width: 100%;" show-action clearable v-model="formData.address" left-icon="" right-icon="" placeholder="片区" readonly>
                  <template #action>
                    <!-- <van-icon size="17" color="#969799" v-if="formData.address!==''" name="close" @click.stop="formData.address=''"/> -->
                    <van-icon name="home-o" color="#969799" size="17"></van-icon>
                  </template>
                </van-search>
              </template>
            </YjSelectTree>
          </div>
          <van-search class="van_search" v-model="selectedDayName" left-icon="" right-icon="home-o" placeholder="日程" @click="showVisitDayClick" readonly />
          <van-search class="van_search"    v-model="formData.arrearsAge" style="width:50%" left-icon="" placeholder="账龄" right-icon="close" @input="searchKeyInput"/>

          <select-one style="width:50%;" placeholder="业务员" :target="'seller'" :formObj="formData" :formFld="'sellerID'"  :formNameFld="'sellerName'" :queryFunc="newQuery" :disabled="formData.sellerDisabled" />

          <select-one style="width:50%;" placeholder="送货员" :target="'sender'" :formObj="formData" :formFld="'senderID'" :formNameFld="'sender_name'" :queryFunc="newQuery" />

          <van-search class="van_search" v-model="times" style="width:100%" left-icon="" placeholder="欠款日期" right-icon="close" readonly @click="timeShow = true" @click-right-icon.stop="clearDate" />
        </van-form>
      </div>
      <div>
        <van-calendar :allow-same-day="true" :min-date="minDate" :max-date="maxDate" v-model="timeShow" type="range" @confirm="confirmDateDuring" />
      </div>
      <!-- 片区弹出层 -->
      <!-- <van-popup style="overflow: hidden !important" v-model="showPopup" position="bottom" safe-area-inset-bottom :style="{ height: '85%', width: '100%' }">
        <region-selection @onRegionSelected="onRegionSelected" :moreSelected="true" :showClearBtn="false"></region-selection>
      </van-popup> -->
      <!-- 日程弹出层 -->
      <van-popup style="overflow: hidden !important" v-model="showVisitDay" position="bottom" round safe-area-inset-bottom :style="{ height: '45%', width: '100%' }">
        <div class="day-container">
          <div @click="dayItemClick(item)" class="day-item" v-for="item,index in visitDays" :key="index">
            {{item.day_name}}
          </div>
        </div>
      </van-popup>
      <van-popup v-model="sellerShow" position="bottom" safe-area-inset-bottom :style="{height:'85%',width:'100%'}">
        <select-seller @selectSellers="selectSeller"></select-seller>
      </van-popup>
      <div style="height:8px;"></div>
        <div class="public_box2_b" v-if="listData && listData.length > 0">
          <div class="list-container">
            <div class="title3" style="background: #f4f4f4;padding-right:5px;padding-left:5px; white-space: nowrap;">
              <div style="width: 50%; display: inline-block;">结算单位</div>
              <div style="width: 25%; text-align: left; display: inline-block;" v-if="viewArrearsRange!='self'">总欠款</div>
              <div style="width: 25%; text-align: left; display: inline-block;">期间欠款</div>             
              <div style="width: 30%; text-align: right; display: inline-block;">期间单据金额</div>
            </div>
            <van-list style="width: 130%;padding-right:5px; padding-left:5px" v-model="loading" :finished="finished" finished-text="到底了~" @load="onNextPage">
                <div v-for="(item, index) in listData" :key="index" @click="selectCollection(item)" style="display: flex; width: 100%;text-align: left;border-bottom: 0.5px solid #dddddd;padding: 10px 0;">
                  <div style="width: 50%; padding-right:5px">{{ item.sup_name }}</div>
                  <div style="width: 25%; text-align: left; padding-right:5px;padding-left:5px" v-if="viewArrearsRange!='self'">{{ toMoney(item.balance) }}</div>
                  <div style="width: 25%; text-align: left; padding-right:5px;padding-left:5px" :style="{ color: item.no_paid_amount != item.balance ? '#f66' : '#999'}">{{ toMoney(item.no_paid_amount) }}</div>
                  <div style="width: 30%; text-align: right; padding-right: 10px;">{{ toMoney(item.total_amount) }}</div>
                </div>
            </van-list>
          </div>
            <div class="receivables_footer" style="white-space: nowrap; display: block;">
              <div class="first_row" style=" height: 22px; width: 100%;">
                <div style="width: 40%; text-align: left; display: inline-block; font-size: 17px;">合计</div> 
                <div style="width: 60%; text-align: right; display: inline-block; font-size: 17px;" >期间欠款￥{{ fix(sum_period_left_amount) }}</div>   
              </div>
              <div class="second_row"  style=" height: 22px; width: 100%; ">
                <div style="width: 50%; text-align: left; display: inline-block; font-size: 17px; " :style="{ color: sum_period_left_amount != sum_balance ? '#f66' : '#999' }" v-if="viewArrearsRange!='self'">总欠款￥{{ fix(sum_balance) }}</div>
                <div style="width: 50%; text-align: right; display: inline-block; margin-left: 0px; font-size: 17px;">成交额￥{{ fix(sum_period_total_amount) }}</div>
              </div>
            </div>
      </div>
      <div class="report_no_box" v-else>
        <div class="whole_box_no_icon iconfont">&#xe664;</div>
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { Icon, NavBar, Search, List, Popup, Form, Calendar, Field, Toast } from "vant";
import { GetArrears, LoadVisitDayList, GetTreesForSelect } from "../../api/api";
import RegionSelection from "../components/RegionSelection";
import SelectSeller from "../components/SelectSeller";
import SelectOne from "../components/SelectOne";
import YjSelectTree from "../components/yjTree/YjSelectTree.vue";

export default {
  name: "Receivables",
  data() {
    return {
      showCalendar: false,
      minDate: new Date(2010, 0, 1),
      maxDate: new Date(),
      loading: false,
      timeShow: false,
      times: "",
      sellerShow: false,
      finished: true,
      listData: [],
      sum_balance: 0,
      sum_period_left_amount: 0,
      sum_period_total_amount: 0,
      recordCount: 0,
      showPopup: false,
      showVisitDay: false,
      visitDays: [],
      selectedDayName: '',
      formData: {
        name: "",//客户名称
        address: "",
        sellerID: "",
        sellerName: "",
        senderID: '',
        arrearsAge:'',
        startDate: "",
        endDate: "",
        addressId: "",
        dayID: "",
        sellerDisabled:false
      },
      userCanSeeAllRegion: true,
      rootRegionId: -1,
      pageSize: 50,
      startRow: 0,
      target:'region',
      rangeKey: 'name',
			idKey: 'id',
      sonNode:'subNodes',
      asPage:true,
			multipleCheck: true,
			parentSelectable: true,
			foldAll: true,
      showClearBtn:false,
			confirmColor:'#e3a2a2',
			title: '片区选择',
      rootNode:{}
    };
  },
  async mounted() {
    this.formData.startDate = this.getDateMonthsAgo(12);
    this.formData.endDate = this.getDatePart(new Date())
    this.times = `${this.formData.startDate} 至 ${this.formData.endDate}`;
    this.getStoreRegionInfo()
    await this.getUserRegionRights()
 
    if(this.viewArrearsRange=='self'){
      //this.formData.name=this.$store.state.operInfo.oper_name
      
      this.formData.sellerID=this.$store.state.operInfo.oper_id
      this.formData.sellerName=this.$store.state.operInfo.oper_name
      this.formData.sellerDisabled=true
      
    }

    this.newQuery()
  },
  components: {
    "van-nav-bar": NavBar,
    "van-search": Search,
    "van-list": List,
    "van-popup": Popup,
    "region-selection": RegionSelection,
    "select-seller": SelectSeller,
    "van-form": Form,
    "van-icon": Icon,
    "van-calendar": Calendar,
    "select-one": SelectOne,
    YjSelectTree
  },
  computed:{
    viewArrearsRange(){
      return window.getRightValue('delicacy.supcustArrearsRange.value')
    }
 
  },
  methods: {
    async getUserRegionRights() {
      const params = {target: 'region'}
      const operRegions = this.$store.state.operInfo.operRegions
      if (operRegions) {
        params.operRegions = JSON.stringify(operRegions)
      } else {
        params.operRegions = ''
      }
      const res = await GetTreesForSelect(params)
      if (res.result === 'OK') {
        const root_node_id = res.data[0]?.id
        if (!root_node_id) return
        this.rootRegionId = root_node_id
        if (operRegions&&operRegions.length>0&&operRegions.indexOf(root_node_id) === -1) {
          this.userCanSeeAllRegion = false
        }
      }
      if(this.userCanSeeAllRegion){
        this.formData.addressId = ""
        this.formData.address = ""
      }
    },
    dayItemClick(item) {
      this.formData.dayID = item.day_id
      this.selectedDayName = item.day_name
      this.newQuery()
      this.showVisitDay = false
    },
    async showVisitDayClick() {
      this.showVisitDay = true
      const res = await LoadVisitDayList()
      this.visitDays = res.data
    },
    getStoreRegionInfo() {
      this.formData.addressId = ""
      this.formData.address = ""
      if (this.$store.state.infoRegionObj.length > 0) {
        this.$store.state.infoRegionObj.forEach(infoRegion => {
          this.formData.addressId += infoRegion.region_id + ","
          this.formData.address += infoRegion.region_name + ','
        })
        this.formData.addressId = this.formData.addressId.slice(0, this.formData.addressId.length - 1)
        this.formData.address = this.formData.address.slice(0, this.formData.address.length - 1)
      } else {
        this.formData.addressId = ""
        this.formData.address = ""
      }

    },
    clearDate() {
      this.formData.startDate = "";
      this.formData.endDate = "";
      this.times = "";
      this.newQuery()
    },
    clearSeller() {
      this.formData.sellerID = "";
      this.formData.sellerName = "";
      this.newQuery()
    },
    confirmDateDuring(e) {
      this.timeShow = false;
      this.formData.startDate = this.formatDate(e[0]);
      this.formData.endDate = this.formatDate(e[1]);
      this.times = `${this.formData.startDate} 至 ${this.formData.endDate}`;
      this.newQuery()
    },
    async newQuery() {
      if (!this.userCanSeeAllRegion
        && (this.formData.addressId == this.rootRegionId || !this.formData.addressId)) {
        Toast.fail('请选择片区')
        return
      }
      this.startRow = 0;
      this.finished = false;
      this.listData = [];
      await this.onNextPage(true);
    },
    async onNextPage(isNewQuery) {
      if (this.finished) return
      var that = this
      if(this.formData.arrearsAge && !Number.isFinite(Number(this.formData.arrearsAge))){
        this.$toast("账龄请填写数字！")
        this.formData.arrearsAge = ""
      }
      let params = {
        dayID: this.formData.dayID,
        pageSize: this.pageSize,
        startRow: this.startRow,
        searchStr: this.formData.name,
        regionID: this.formData.addressId,
        sellerID: this.formData.sellerID,
        senderID: this.formData.senderID,
        arrearsAge:this.formData.arrearsAge,
        startTimeStr: this.formData.startDate,
        endTimeStr: this.formData.endDate?  this.formData.endDate +' '+'23:59:59' :this.formData.endDate 
      };
      that.startRow += that.pageSize
      await GetArrears(params).then((res) => {
        if (res.result === "OK") {
          var n = res.recordCount
          if (n >= 0) {
            that.recordCount = n
            that.sum_balance = res.sum_balance
            that.sum_period_left_amount = res.sum_period_left_amount
            that.sum_period_total_amount = res.sum_period_total_amount
          }
          if (isNewQuery) {
            that.listData = []
          }
          that.listData.splice(that.listData.length, 0, ...res.data)
          if (that.listData.length >= that.recordCount) {
            that.finished = true
          }
        }
        that.loading = false
      })
    },
    searchKeyInput() {
      if (this.tmInput) {
        clearTimeout(this.tmInput)
        this.tmInput = null
      }
      this.tmInput = setTimeout(() => {
        this.newQuery()
      }, 300)
    },
    selectCollection(item) {
      window.g_listItem = item;
      this.$router.push({
        path: "/GetAndPayArrearsSheet?sheetType=SK/",
        query: { supcust_id: item.supcust_id, sup_name: item.sup_name, startDate: this.formData.startDate, endDate: this.formData.endDate, sellerID: this.formData.sellerID, sellerName: this.formData.sellerName },
      });
    },
    getRootNode(node) {
      this.rootNode=node
    },
    onRegionSelected(obj) {
      if(obj.length>0){
        let regionID=[];
        let regionName=[];
        obj.forEach((item)=>{
          regionID.push(item.id)
          regionName.push(item.name)
        })
        this.formData.address = regionName.join(',');
        this.formData.addressId = regionID.join(',');
      }else {
        this.formData.address = "";
        this.formData.addressId = "";
      }
      // if (obj.regionID) {
      //   this.formData.address = obj.regionName.join(',');
      //   this.formData.addressId = obj.regionID.join(',');
      // } else {
      //   this.formData.addressId = "";
      //   this.formData.address = "";
      // }
      console.log("FormData",this.formData)
      this.$refs.selectTreeRef.handleCancel()
      this.newQuery()
    },
    selectSeller(value) {
      this.sellerShow = value.isSellerShow;
      if (value.oper_id) {
        this.formData.sellerName = value.oper_name;
        this.formData.sellerID = value.oper_id;
      } else {
        this.formData.sellerID = "";
        this.formData.sellerName = "";
      }
      this.newQuery()
    },
    fix(num, n = 2) {
      var pn = Math.pow(10, n);
      return Math.round(Number(num) * pn) / pn;
    },
  },
};
</script>
<style lang="less" scoped>
.pages {
  display: flex;
  flex-direction: column;

  .public_box2 {
    height: 100%;
    width: 100%;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;

    > div {
      flex-shrink: 0;
    }

    .public_box2_b {
      height: 100%;
      width: 100%;
      overflow: hidden;
      flex: 1;
      display: flex;
      flex-direction: column;

      .list-container {
        flex: 1;
        height: 100%;
        width: 100%;
        overflow: auto;
        position: relative;

        .title3,
        .van_list {
          width: 130%;
          padding-right:5px;
          padding-left:5px
        }

        .title3 {
          position: sticky;
          top: 0;
          left: 0;
        }
      }
    }
  }
}

@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex-left: {
  text-align: left;
};
@flex-right: {
  text-align: right;
};
@flex_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
};
.report_no_box {
  width: 100%;
  height: calc(100% - 54px);
  @flex_flex();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon {
    font-size: 50px;
  }
  p {
    font-size: 14px;
  }
}
/deep/.van-cell {
  font-size: 15px;
}
/deep/.van-field__control--right{
  text-align: left;
}

.van-search__content {
  background: #fff;
  border-bottom: 0.5px solid #dddddd;
}
.public_box2_t {
  overflow: hidden;
}
.van_search {
  height: 45px !important;
  width: 50%;
  background: #fff;
  float: left;
}
.list_ul {
  padding: 0 5px;
  background: #ffffff;
  //style="white-space: nowrap;"
  li {
    display: flex;
    text-align: left;
    border-bottom: 0.5px solid #dddddd;
    padding: 10px 0;
  }
  li:last-child {
    border: none;
  }
}
.receivables_footer {
  width:100%;
  height: 60px;
  display: flex;
  background: #f4f4f4;
  box-sizing: border-box;
  padding: 5px 10px;
  font-size: 13px;
  border-top: 1px solid #f2f2f2;
  box-shadow: 0 2px 5px #f2f6fc;
  padding-left: 5px;
  padding-right: 15px;
}

.day-container {
  float: left;
  width: 100%;
  max-height: 100%; 
  overflow-y: auto; 
}
.day-item {
  margin: 20px;
  border-bottom: 0.5px solid #eee;
}
</style>
