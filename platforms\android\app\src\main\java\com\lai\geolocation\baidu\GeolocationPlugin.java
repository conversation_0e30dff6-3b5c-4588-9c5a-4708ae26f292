package com.lai.geolocation.baidu;

import org.apache.cordova.CallbackContext;
import org.apache.cordova.CordovaPlugin;
import org.apache.cordova.PluginResult;
import org.apache.cordova.PermissionHelper;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.baidu.location.BDLocation;
import com.baidu.location.BDLocationListener;
import com.fasterxml.jackson.databind.util.BeanUtil;
import com.lai.geolocation.w3.PositionOptions;

import android.app.Activity;
import android.content.Context;
import android.location.Address;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Build;
import android.os.Bundle;

import android.util.Log;
import android.util.SparseArray;
import android.Manifest;
import android.content.pm.PackageManager;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;

import java.util.List;

public class GeolocationPlugin extends CordovaPlugin {

  private static final String TAG = "GeolocationPlugin";

  private static final int GET_CURRENT_POSITION = 0;
  private static final int WATCH_POSITION = 1;
  private static final int CLEAR_WATCH = 2;
    private static final int GET_NATIVE_CURRENT_POSITION = 3;


  private Location curLocation = null;

  private SparseArray<BDGeolocation> store = new SparseArray<BDGeolocation>();
  private String [] permissions = { Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.ACCESS_FINE_LOCATION };

  private JSONArray requestArgs;
  private CallbackContext context;

  @Override
  public boolean execute(String action, JSONArray args, CallbackContext callbackContext) throws JSONException {
    Log.i(TAG, "插件调用");
    JSONObject options = new JSONObject();

    requestArgs = args;
    context = callbackContext;

      switch (action) {
          case "getCurrentPosition":
              getPermission(GET_CURRENT_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              return getCurrentPosition(options, callbackContext);
          case "getNativePosition":
              getPermission(GET_CURRENT_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                  return getNativePosition(options, callbackContext);
              }
              break;
          case "watchPosition": {
              getPermission(WATCH_POSITION);
              try {
                  options = args.getJSONObject(0);
              } catch (JSONException e) {
                  Log.v(TAG, "options 未传入");
              }
              int watchId = args.getInt(1);
              return watchPosition(options, watchId, callbackContext);
          }
          case "clearWatch": {
              getPermission(CLEAR_WATCH);
              int watchId = args.getInt(0);
              return clearWatch(watchId, callbackContext);
          }
          case "openFrontLocationService": {
              int watchId = args.getInt(0);
              return openFrontLocationService(watchId);
          }
          case "closeFrontLocationService": {
              int watchId = args.getInt(0);
              return closeFrontLocationService(watchId);
          }
      }
    return false;
  }

    private boolean openFrontLocationService(int watchId) {
        Log.i(TAG, "开启前台定位服务");
        BDGeolocation geolocation = store.get(watchId);
        geolocation.openFrontLocationService();
        return true;
    }

  private boolean closeFrontLocationService(int watchId) {
      Log.i(TAG, "关闭前台定位服务，同时移除通知栏");
      BDGeolocation geolocation = store.get(watchId);
      geolocation.closeFrontLocationService();
      return true;
  }

  private boolean clearWatch(int watchId, CallbackContext callback) {
    Log.i(TAG, "停止监听");
    BDGeolocation geolocation = store.get(watchId);
    store.remove(watchId);
    geolocation.clearWatch();
    callback.success();
    return true;
  }

  private boolean watchPosition(JSONObject options, int watchId, final CallbackContext callback) {
    Log.i(TAG, "监听位置变化");
    Activity activity = cordova.getActivity();
    Context ctx = activity.getApplicationContext();
    PositionOptions positionOpts = new PositionOptions(options);
    BDGeolocation geolocation = new BDGeolocation(ctx, activity);
    store.put(watchId, geolocation);
    return geolocation.watchPosition(positionOpts, new BDLocationListener() {
      @Override
      public void onReceiveLocation(BDLocation location) {
        JSONArray message = new MessageBuilder(location).build();
        PluginResult result = new PluginResult(PluginResult.Status.OK, message);
        result.setKeepCallback(true);
        callback.sendPluginResult(result);
      }
    });
  }

  private boolean getCurrentPosition(JSONObject options, final CallbackContext callback) {
    Log.i(TAG, "请求当前地理位置");
    Activity activity = cordova.getActivity();
    Context ctx = activity.getApplicationContext();
    PositionOptions positionOpts = new PositionOptions(options);
    BDGeolocation geolocation = new BDGeolocation(ctx, activity);
    return geolocation.getCurrentPosition(positionOpts, new BDLocationListener() {
      @Override
      public void onReceiveLocation(BDLocation location) {
        JSONArray message = new MessageBuilder(location).build();
        callback.success(message);
      }
    });
  }
    // 原来的 getNativePosition 方法（已注释，存在缓存问题）
    /*
    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean getNativePosition_OLD(JSONObject options, final CallbackContext callback) {
        Log.i(TAG, "请求当前地理位置");
        Activity activity = cordova.getActivity();
        Context ctx = activity.getApplicationContext();
        PositionOptions positionOpts = new PositionOptions(options);
        LocationManager mLocationManager = (LocationManager) ctx.getSystemService(Context.LOCATION_SERVICE ); // 位置
         String locationProvider;

        List<String> providers = mLocationManager.getProviders( true );
        if (providers.contains( LocationManager.NETWORK_PROVIDER )) {
            //如果是网络定位
            Log.d( TAG, "如果是网络定位" );
            locationProvider = LocationManager.NETWORK_PROVIDER;
        } else if (providers.contains( LocationManager.GPS_PROVIDER )) {
            //如果是GPS定位
            Log.d( TAG, "如果是GPS定位" );
            locationProvider = LocationManager.GPS_PROVIDER;
        } else {
            locationProvider = "";
            Log.d( TAG, "没有可用的位置提供器" );
        }
        // 需要检查权限,否则编译报错,想抽取成方法都不行,还是会报错。只能这样重复 code 了。
        if (Build.VERSION.SDK_INT >= 23 &&
                ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_FINE_LOCATION ) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_COARSE_LOCATION ) != PackageManager.PERMISSION_GRANTED) {
        }
        if (ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_FINE_LOCATION ) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission( ctx, Manifest.permission.ACCESS_COARSE_LOCATION ) != PackageManager.PERMISSION_GRANTED) {
        }
        //3.获取上次的位置，一般第一次运行，此值为null
        BDLocation bdLocation = new BDLocation();

        // 监视地理位置变化，第二个和第三个参数分别为更新的最短时间minTime和最短距离minDistace
        mLocationManager.requestLocationUpdates( locationProvider, 200, 0, locationListener );
        if (curLocation != null) {
            bdLocation = toBDLocation( curLocation );
        }else {
            Location location = mLocationManager.getLastKnownLocation( locationProvider );
            bdLocation = toBDLocation( location );
        }
        JSONArray message = new MessageBuilder(bdLocation).build();
        callback.success(message);
        return true;
    }
    */

    // 新的 getNativePosition 方法（解决缓存问题，获取实时位置）
    @RequiresApi(api = Build.VERSION_CODES.M)
    private boolean getNativePosition(JSONObject options, final CallbackContext callback) {
        Log.i(TAG, "请求当前地理位置（原生定位-实时模式）");
        Activity activity = cordova.getActivity();
        Context ctx = activity.getApplicationContext();

        LocationManager mLocationManager = (LocationManager) ctx.getSystemService(Context.LOCATION_SERVICE);
        String locationProvider;

        // 选择最佳的位置提供器，优先GPS
        List<String> providers = mLocationManager.getProviders(true);
        if (providers.contains(LocationManager.GPS_PROVIDER)) {
            locationProvider = LocationManager.GPS_PROVIDER;
            Log.d(TAG, "使用GPS定位");
        } else if (providers.contains(LocationManager.NETWORK_PROVIDER)) {
            locationProvider = LocationManager.NETWORK_PROVIDER;
            Log.d(TAG, "使用网络定位");
        } else {
            Log.d(TAG, "没有可用的位置提供器");
            callback.error("没有可用的位置提供器");
            return false;
        }

        // 检查权限
        if (Build.VERSION.SDK_INT >= 23 &&
                ActivityCompat.checkSelfPermission(ctx, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(ctx, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            callback.error("缺少定位权限");
            return false;
        }

        // 创建一次性的位置监听器
        LocationListener singleLocationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                Log.i(TAG, "获取到实时位置: " + location.getLatitude() + ", " + location.getLongitude());
                BDLocation bdLocation = toBDLocation(location);
                JSONArray message = new MessageBuilder(bdLocation).build();
                callback.success(message);

                // 停止位置更新
                try {
                    mLocationManager.removeUpdates(this);
                } catch (SecurityException e) {
                    Log.e(TAG, "停止位置更新失败: " + e.getMessage());
                }
            }

            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {}

            @Override
            public void onProviderEnabled(String provider) {}

            @Override
            public void onProviderDisabled(String provider) {
                callback.error("位置提供器被禁用: " + provider);
            }
        };

        try {
            // 请求单次位置更新，不使用缓存
            mLocationManager.requestSingleUpdate(locationProvider, singleLocationListener, null);

            // 设置超时机制
            cordova.getThreadPool().execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(10000); // 等待10秒
                        // 如果10秒后还没有获取到位置，尝试返回最后已知位置
                        try {
                            mLocationManager.removeUpdates(singleLocationListener);
                            Location lastLocation = mLocationManager.getLastKnownLocation(locationProvider);
                            if (lastLocation != null) {
                                BDLocation bdLocation = toBDLocation(lastLocation);
                                JSONArray message = new MessageBuilder(bdLocation).build();
                                callback.success(message);
                                Log.i(TAG, "超时返回最后已知位置");
                            } else {
                                callback.error("定位超时且无最后已知位置");
                            }
                        } catch (SecurityException e) {
                            callback.error("获取位置失败: " + e.getMessage());
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });

        } catch (SecurityException e) {
            Log.e(TAG, "请求位置更新失败: " + e.getMessage());
            callback.error("请求位置更新失败: " + e.getMessage());
            return false;
        }

        return true;
    }
    /**
     * LocationListern监听器
     * 参数：地理位置提供器、监听位置变化的时间间隔、位置变化的距离间隔、LocationListener监听器
     */
    private BDLocation toBDLocation(Location location) {
        BDLocation bdLocation = new BDLocation();
        bdLocation.setLatitude(location.getLatitude());
        bdLocation.setLongitude(location.getLongitude());
        bdLocation.setAltitude(location.getAltitude());
        bdLocation.setAddrStr("");
        bdLocation.setAddr(null);
        bdLocation.setLocationDescribe("");
        return bdLocation;
    }
    LocationListener locationListener = new LocationListener() {

        /**
         * 当某个位置提供者的状态发生改变时
         */
        @Override
        public void onStatusChanged(String provider, int status, Bundle arg2) {

        }

        /**
         * 某个设备打开时
         */
        @Override
        public void onProviderEnabled(String provider) {

        }

        /**
         * 某个设备关闭时
         */
        @Override
        public void onProviderDisabled(String provider) {

        }

        /**
         * 手机位置发生变动
         */
        @Override
        public void onLocationChanged(Location location) {
            location.getAccuracy();//精确度
            curLocation = location;
//            setLocation( location );
        }


    };

  /**
   * 获取对应权限
   * int requestCode Action代码
   */
  public void getPermission(int requestCode){
    if(!hasPermisssion()){
      PermissionHelper.requestPermissions(this, requestCode, permissions);
    }
  }

  /**
   * 权限请求结果处理函数
   * int requestCode Action代码
   * String[] permissions 权限集合
   * int[] grantResults 授权结果集合
   */
  @RequiresApi(api = Build.VERSION_CODES.M)
  public void onRequestPermissionResult(int requestCode, String[] permissions,
                                        int[] grantResults) throws JSONException
   {
       PluginResult result;
       //This is important if we're using Cordova without using Cordova, but we have the geolocation plugin installed
       if(context != null) {
           for (int r : grantResults) {
               if (r == PackageManager.PERMISSION_DENIED) {
                   Log.d(TAG, "Permission Denied!");
                   result = new PluginResult(PluginResult.Status.ILLEGAL_ACCESS_EXCEPTION);
                   context.sendPluginResult(result);
                   return;
               }

           }
           switch(requestCode)
           {
               case GET_NATIVE_CURRENT_POSITION:
                   getNativePosition(this.requestArgs.getJSONObject(0), this.context);
                   break;
               case GET_CURRENT_POSITION:
                   getCurrentPosition(this.requestArgs.getJSONObject(0), this.context);
                   break;
               case WATCH_POSITION:
                   watchPosition(this.requestArgs.getJSONObject(0), this.requestArgs.getInt(1), this.context);
                   break;
               case CLEAR_WATCH:
                   clearWatch(this.requestArgs.getInt(0), this.context);
                   break;
           }
       }
   }

   /**
    * 判断是否有对应权限
    */
   public boolean hasPermisssion() {
       for(String p : permissions)
       {
           if(!PermissionHelper.hasPermission(this, p))
           {
               return false;
           }
       }
       return true;
   }

   /*
    * We override this so that we can access the permissions variable, which no longer exists in
    * the parent class, since we can't initialize it reliably in the constructor!
    */

   public void requestPermissions(int requestCode)
   {
       PermissionHelper.requestPermissions(this, requestCode, permissions);
   }

}
