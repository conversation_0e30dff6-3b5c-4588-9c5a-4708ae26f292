<template>
  <div class="pages">
    <div class="goodes_box" v-if="itemList && itemList.length>0">
      <van-list v-model="loading" :finished="finished" finished-text="到底了~" @load="loadNextPage">
        <ul class="product_ul">
          <van-swipe-cell v-for="(item,index) in itemList" :key="index">
            <li @click="onItemClick(item)">
              <div class="item-name-status-wrapper">
                <!-- <div v-if="handleImage(item.item_images).tiny !== ''" class="content-img" @click.stop="handleItemImageClick(item.item_images)">
                  <van-image width="80px" height="80px" lazy-load :src="handleImage(item.item_images).tiny" />
                </div> -->
                <!-- <div v-if="!isNoImagesInAllItems" class="content-img" @click.stop="handleItemImageClick(item.item_images)">
                  <van-image width="80" height="80" class="content-img" lazy-load :src="(item.item_images && item.item_images.tiny !== '') ? handleImage(item.item_images).tiny : require('@/assets/images/default_good_img.png')" fit="resize" />
                </div> -->
                <div v-if="!isNoImagesInAllItems" class="content-img" @click.stop="handleItemImageClick(item.item_images)">
                  <van-image width="80" height="80" class="content-img" lazy-load
                    :src="handleImage(item.item_images).tiny !== '' ? handleImage(item.item_images).tiny : require('@/assets/images/default_good_img.png')"
                    fit="resize" />
                </div>
                <template v-if="item.status === '0'">
                  <div class="status-wrapper">停用</div>
                </template>
                <div style="width:100%;padding-left:5px; ">
                  <h4 style="margin-top: 5px; margin-bottom: 10px">{{ item.item_name }}</h4>
                  <h6>
                    <span v-if="item.unit_no" style="font-size:13px">{{
                      item.unit_conv.includes('=')?item.unit_conv:item.unit_no}}</span>
                    <span id="wholesale_price" v-if="item.wholesale_price" style="floar:right;font-size:13px">￥{{
                      item.wholesale_price }}/{{item.unit_no}}</span>
                  </h6>
                  <h6>
                    <span></span>
                    <span id="s_wholesale_price" v-if="item.s_wholesale_price && sUnitPriceShow"
                      style="floar:right;font-size:13px">￥{{ item.s_wholesale_price }}/{{item.s_unit_no}}(小)</span>
                  </h6>
                </div>

              </div>
              <div style="font-size: 14px; color: #aaa;text-align: end" v-if="item.s_barcode">
                <span style="font-size: 14px">小:{{item.s_barcode}}</span>
              </div>
            </li>
            <template v-if="editInfoItem" #right>
              <van-button style="height: inherit;" square type="danger" text="删除" @click="itemDel(item)" />
            </template>
          </van-swipe-cell>
        </ul>
      </van-list>
    </div>
    <div class="goodes_no_box" v-else>
      <div class="whole_box_no_icon iconfont">
        &#xe664;
      </div>
      <p>未查询到信息</p>
    </div>
    <div class="wrapper">
      <div class="content">共<span class="record">{{ itemCount }}</span>条</div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import { GetGoodsItemList, GetItemInfoDetail, DeleteRecords } from "../../api/api"
import { List, SwipeCell, Button, Dialog, Toast, Image as VanImage, Lazyload, ImagePreview } from 'vant'
import globalVars from "../../static/global-vars";
Vue.use(Lazyload)
export default {
  name: "ItemDetail",
  data() {
    return {
      searchStr: '',
      brandID: '',
      classID: '',
      loading: false,
      finished: false,
      pageSize: 20,
      startRow: 0,
      classIDActive: '',
      branchID: '',
      srcDatas: {},
      itemList: [],
      itemCount: 0,
      sUnitPriceShow:false,
    }
  },
  props: {
    itemName: String,
    queryParams: Object
  },
  watch: {
    'itemName': function () {
      this.startNewPage()
    },
    queryParams: {
      handler: function (val, oldVal) {
        this.startNewPage()
      },
      deep: true
    },

    '$store.state.motherId': function () {
      this.startNewPage()
    },
    '$route'(to, from) {
      if (from.path === '/GoodsArchivesSon') {
        console.log('itemArchivePageOption', this.$store.state.itemArchivePageOption)
        if (this.$store.state.itemArchivePageOption) {
          if (this.$store.state.itemArchivePageOption.type === 'update') {
            const formData = this.$store.state.itemArchivePageOption.formData
            let changeItem = this.itemList.find(item => item.item_id === formData.item_id)
            if (changeItem) {
              // 使用this.$set更新对象属性
              this.$set(changeItem, 'status', formData.status);
              this.$set(changeItem, 'item_images', formData.item_images);
              this.$set(changeItem, 'item_name', formData.item_name);
              this.$set(changeItem, 'unit_no', formData.bunit || formData.sunit);
              this.$set(changeItem, 'wholesale_price', formData.bpprice); // 修正wholesale_price的赋值
              this.$set(changeItem, 's_wholesale_price', formData.spprice);
              this.$set(changeItem, 's_unit_no', formData.sunit);
              this.$set(changeItem, 's_barcode', formData.sbarcode);

              // 更新列表中的项目
              const itemIndex = this.itemList.findIndex(item => item.item_id === formData.item_id);
              if (itemIndex > -1) {
                this.itemList.splice(itemIndex, 1, changeItem);
              }

              // 强制触发数组更新
              this.itemList = [...this.itemList];
            }
          }
          // 重置状态（只需要执行一次）
          this.$store.commit('itemArchivePageOption', null)
        }
      }
    }
  },
  mounted() {
    //this.startNewPage()
    // 不使用缓存，使用默认值
    this.sUnitPriceShow = false;
  },
  activated() {
    //this.startNewPage()
  },
  components: {
    "van-list": List,
    "van-swipe-cell": SwipeCell,
    "van-button": Button,
    "van-image": VanImage,
    [ImagePreview.Component.name]: ImagePreview.Component
  },
  computed: {
    editInfoItem() {
      return hasRight('info.infoItem.edit')
    },
    deleteAuthority() {
      let auth = getRightValue('info.infoItem.delete')
      if (auth == "false") {
        return false
      } else {
        return true
      }
    },
    approveAuthority() {
      let auth = getRightValue('info.infoItem.approve')
      if (auth == "false") {
        return false
      } else {
        return true
      }
    },
    // 判断当前页面是否所有商品都没有图片 如果都没有 则不显示图片
    isNoImagesInAllItems() {
      if (Array.isArray(this.itemList)) {
        return this.itemList.every(item => {
          if (item.item_images) {
            let obj = JSON.parse(item.item_images); // 解析图片 JSON
            obj.main = obj.main ? globalVars.obs_server_uri + '/' + obj.main : '';
            obj.tiny = obj.tiny ? globalVars.obs_server_uri + '/' + obj.tiny : '';
            if (obj.other) {
              obj.other.forEach((img, index) => {
                if (img) {
                  obj.other[index] = globalVars.obs_server_uri + '/' + img;
                }
              });
              obj.other.unshift(obj.main); // 将主图添加到其他图片列表的开头
            }
            // 判断是否没有有效图片（`tiny`为空或不存在）
            return obj.tiny === '';
          }
          return true; // 没有图片时认为是没有有效图片
        });
      }
      return false;
    }



  },
  methods: {
    startNewPage() {
      this.startRow = 0
      this.finished = false
      this.itemList = []
      setTimeout(() => {
        this.loadNextPage()
      }, 100)
    },
    loadNextPage() {
      // 如果已经加载完成，则不再加载
      if (this.finished) return;

      // 设置默认的请求参数
      let params = {
        searchStr: this.itemName ? this.itemName : '',
        brandID: this.$store.state.operInfo.brands_id,
        classID: this.$store.state.motherId ? this.$store.state.motherId : '',
        pageSize: this.pageSize,
        startRow: this.startRow,
        queryByBrandId: false,
        status: '1', // 默认状态
        sortFld1: 'order_index', // 默认排序字段1
        sortFld2: 'item_name', // 默认排序字段2
        sortFld3: 'none', // 默认排序字段3
      };

      // 如果有额外的查询参数（`queryParams`），覆盖默认值
      if (JSON.stringify(this.queryParams) !== "{}") {
        // 覆盖 sortFld1, sortFld2, sortFld3 的逻辑
        params.sortFld1 = this.queryParams.sortFld1 || 'order_index'; // 默认值为 'order_index'
        params.sortFld2 = this.queryParams.sortFld2 || 'item_name';   // 默认值为 'item_name'
        params.sortFld3 = this.queryParams.sortFld3 || 'none';        // 默认值为 'none'

        // 覆盖其他查询条件
        if (this.queryParams.searchStr) params.searchStr = this.queryParams.searchStr;
        if (this.queryParams.brand_id) {
          params.brandID = this.queryParams.brand_id;
          params.queryByBrandId = true;
        }
        if (this.queryParams.class_id && this.queryParams.class_id !== '0') params.classID = this.queryParams.class_id;
        if (this.queryParams.status) params.status = this.queryParams.status;
      }

      // 发送请求获取数据
        GetGoodsItemList(params)
        .then((res) => {
          if (res.result === "OK") {
            // 如果是第一页，初始化总数
            if (this.startRow === 0) this.itemCount = res.itemCount;

            // 将返回的商品数据添加到列表中
            res.data.forEach(item => {
              this.itemList.push(item);
            });

            // 更新加载状态
            this.loading = false;
            this.startRow = Number(this.startRow) + this.pageSize;

            // 检查是否已经加载完所有数据
            if (this.itemList.length >= Number(this.itemCount)) {
              this.finished = true;
            }
          }
        })
        .catch((error) => {
          console.error("加载商品列表失败:", error);
          this.loading = false;
        });
    },

    onItemClick(item) {
      // if (!this.editInfoItem) return
      this.itemID = item.item_id
      let params = {
        itemID: item.item_id
      }
      let goodsDetailData = {}
       this.$router.push({ path: '/GoodsArchivesSon' , query: {itemID:item.item_id}})
      //GetItemInfoDetail(params).then(res => {
      //  if (res.result === 'OK') {
      //    goodsDetailData = res.data
      //    goodsDetailData.item_id = this.itemID
      //    goodsDetailData.itemUsed = res.itemUsed
      //    goodsDetailData.attrOptions = res.attrOptions
      //    goodsDetailData.availAttributes = res.availAttributes
      //    goodsDetailData.plans = res.plans
      //    this.$router.push({ path: '/GoodsArchivesSon', query: {goodsDetailData:goodsDetailData,editLog: res.editLog} })
      //  } else {
      //    this.$router.push({ path: '/GoodsArchivesSon' })
      //  }
      //})
    },
    addRow(row) {
      this.itemList.splice(0, 0, row)
    },
    itemDel(item) {
      if(!this.deleteAuthority){
        Toast.fail("暂无删除权限")
        return
      }
      Dialog.confirm({
        title: item.item_name,
        message: '确认删除该商品吗?',
        width:"320px"
      })
        .then(() => {
          // on confirm
          let params = {
            rowIDs: item.item_id,
            itemName:item.item_name,
            operKey: this.$store.state.operKey
          }
          console.log(this.approveAuthority)
         params.approveStatus = this.approveAuthority?"DELETE_AND_APPROVED":"DELETE"
          DeleteRecords(params).then(res => {
            console.log(res);
            if (res.result === "OK") {
              Toast.success('删除成功');
              this.startNewPage()
            } else if (res.result === "Error") {
              Toast.fail(res.msg);
            }
          }).catch(err => {
            console.log(err)
          })
        })

    },
    handleImage(itemImages) {
      let obj = {
        main: "",
        tiny: "",
        other: []
      }
      if (itemImages) {
        obj = JSON.parse(itemImages)
        obj.main = obj.main ? globalVars.obs_server_uri + '/' + obj.main : ''
        obj.tiny = obj.tiny ? globalVars.obs_server_uri + '/' + obj.tiny : ''
        if(obj.other){
                for (let i = 0; i < obj.other.length; i++) {
              if (obj.other[i]) {
                obj.other[i] = globalVars.obs_server_uri + '/' + obj.other[i]
              }
            }
            obj.other.unshift(obj.main)
        }

      }
      return obj
    },
    async handleItemImageClick(itemImages){
      let showImages=await this.handleImage(itemImages)
      // console.log(showImages)
      this.$bus.$emit("handleItemImagePreview",showImages)
    },
    handleShowSunitPrice(sUnitPriceCheck){
      this.sUnitPriceShow = sUnitPriceCheck
    }
  }
}
</script>

<style lang="less" scoped>
@flex_a_flex: {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
};
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jfs: {
  display: flex;
  align-items: center;
  justify-content: flex-start;
};
.goodes_box {
  height: calc(100% - 50PX);
  background: #ffffff;
  overflow-y: auto;
  overflow-x: hidden;
}
.goodes_no_box {
  height: 100%;
  @flex_a_flex();
  // background: #f2f2f2;
  color: #ffffff;

  .whole_box_no_icon {
    font-size: 50px;
  }

  p {
    font-size: 14px;
  }
}
.product_ul {
  height: auto;
  overflow: hidden;
  background: #ffffff;
  li {
    border-bottom: 1px solid #f2f2f2;
    padding: 8px 0px;
    // .content-img {
    //   border: 5px solid rgb(255, 255, 255);
    // }
    .item-name-status-wrapper {
      display: flex;
      .status-wrapper {
        font-size: 10px !important;
        color: black;
        border: 1px solid rgb(238, 42, 42);
        padding: 1px 2px;
        margin-right: 3px;
        border-radius: 5px;
        margin-bottom: 2px;
      }
      h4 {
        font-size: 15px;
        text-align: left;
        font-weight: normal;
        @flex_acent_jfs();
        margin-top: 10px;
      }
    }
    h6 {
      // wholesale_price{
      //   margin-right: 100px;
      // }
      font-size: 13px;
      font-weight: normal;
      @flex_acent_jbw();
      color: #666666;
    }
  }
}
.wrapper {
  position: fixed;
  left: 0px;
  bottom: 0px;
  width: 100%;
  height: 50PX;
  font-size: 16PX;
  // padding-bottom: 15px;
  color: #333;
  border-top: 1PX solid #f2f2f2;
  box-shadow: 0 -2PX 5PX #f2f6fc;
  background-color: #fff;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  .content {
    padding-right: 15PX;
  }

  .record {
    padding: 0 10PX;
  }
}
</style>
