# 送货签收路线规划功能实现说明

## 功能概述
在送货签收页面添加了路线规划功能，使用百度地图API为送货员提供智能的配送路线规划，提高配送效率。

## 功能特性

### 1. 路线规划按钮
- **位置**: 在"未签收"标签页右下角显示悬浮按钮
- **样式**: 圆形按钮，带有导航图标和阴影效果
- **显示条件**: 仅在"未签收"标签页显示

### 2. 智能路线规划
- **地图显示**: 使用百度地图展示所有订单位置
- **当前位置**: 自动获取并显示送货员当前位置
- **路线绘制**: 连接所有配送点，显示完整配送路线
- **距离计算**: 实时计算总配送距离

### 3. 订单管理
- **订单列表**: 显示所有待配送订单信息
- **拖拽排序**: 支持手动调整配送顺序
- **智能优化**: 基于贪心算法自动优化配送路线
- **位置筛选**: 自动过滤无位置信息的订单

### 4. 交互功能
- **地图标记**: 每个订单在地图上显示序号标记
- **信息窗口**: 点击标记显示订单详细信息
- **实时更新**: 调整顺序后实时更新地图路线
- **视野调整**: 自动调整地图视野包含所有订单

## 技术实现

### 1. 文件结构
```
app/src/views/DeliveryReceipt/
├── DeliveryReceipt.vue          # 主页面，添加路线规划按钮
└── DeliveryRoutePlanning.vue    # 路线规划组件
```

### 2. 核心组件

#### DeliveryReceipt.vue 修改
- 添加路线规划按钮
- 集成路线规划弹窗
- 处理路线确认逻辑

#### DeliveryRoutePlanning.vue 新增
- 百度地图集成
- 订单列表管理
- 路线优化算法
- 用户交互处理

### 3. API接口

#### 新增API
```javascript
// 保存送货路线顺序
export const SaveDeliveryRouteOrder = (params) => {
  return webApiPost_retry("AppApi/AppOrderManage/SaveDeliveryRouteOrder", params)
}
```

#### 参数格式
```javascript
{
  operKey: "租户标识",
  routeData: [
    {
      order_sheet_id: "订单ID",
      route_order: 1,           // 配送顺序
      addr_lng: "经度",
      addr_lat: "纬度"
    }
    // ... 更多订单
  ]
}
```

### 4. 地图功能

#### 百度地图配置
- **API密钥**: 使用现有的百度地图API密钥
- **地图控件**: 导航控件、比例尺控件
- **交互功能**: 缩放、拖拽、信息窗口

#### 标记样式
- **当前位置**: 蓝色圆形标记
- **订单位置**: 带序号的彩色标记
- **路线**: 蓝色折线连接所有点

### 5. 路线优化算法

#### 贪心算法实现
```javascript
async greedyOptimization() {
  const unvisited = [...this.sortedOrders];
  const optimized = [];
  let currentPoint = this.currentPosition;

  while (unvisited.length > 0) {
    // 找到距离当前位置最近的订单
    let nearestIndex = 0;
    let minDistance = Infinity;

    unvisited.forEach((order, index) => {
      const orderPoint = new BMap.Point(order.addr_lng, order.addr_lat);
      const distance = this.map.getDistance(currentPoint, orderPoint);
      
      if (distance < minDistance) {
        minDistance = distance;
        nearestIndex = index;
      }
    });

    // 移动到最近的订单
    const nearestOrder = unvisited.splice(nearestIndex, 1)[0];
    optimized.push(nearestOrder);
    currentPoint = new BMap.Point(nearestOrder.addr_lng, nearestOrder.addr_lat);
  }

  this.sortedOrders = optimized;
}
```

## 用户操作流程

### 1. 打开路线规划
1. 进入送货签收页面
2. 切换到"未签收"标签
3. 点击右下角"路线规划"按钮

### 2. 查看和调整路线
1. 查看地图上的订单分布
2. 查看当前配送路线和总距离
3. 手动调整订单配送顺序
4. 或点击"智能优化"自动优化路线

### 3. 确认路线
1. 确认配送顺序无误
2. 点击"确定路线"按钮
3. 系统保存路线顺序到服务器
4. 返回订单列表，按新顺序显示

## 错误处理

### 1. 数据验证
- 检查是否有待配送订单
- 验证订单位置信息完整性
- 确保地图API正常加载

### 2. 异常处理
- 地图加载失败提示
- 定位权限获取失败处理
- 网络请求失败重试机制

### 3. 用户提示
- 无订单时的友好提示
- 缺少位置信息的警告
- 操作成功/失败的反馈

## 性能优化

### 1. 地图优化
- 按需加载百度地图API
- 复用地图实例
- 及时清理地图覆盖物

### 2. 算法优化
- 贪心算法时间复杂度O(n²)
- 适合中小规模订单集合
- 可扩展为更复杂的优化算法

### 3. 用户体验
- 异步加载减少阻塞
- 加载状态提示
- 平滑的动画过渡

## 扩展功能建议

### 1. 高级路线优化
- 集成百度地图路线规划API
- 考虑实时交通状况
- 支持多种优化策略

### 2. 配送时间预估
- 基于历史数据预估配送时间
- 考虑订单处理时间
- 提供时间窗口建议

### 3. 实时跟踪
- GPS轨迹记录
- 配送进度实时更新
- 异常情况预警

### 4. 数据分析
- 配送效率统计
- 路线优化效果分析
- 配送成本计算

## 注意事项

### 1. 权限要求
- 需要定位权限获取当前位置
- 需要网络权限访问地图API

### 2. 兼容性
- 支持主流移动浏览器
- 兼容不同屏幕尺寸
- 适配iOS和Android平台

### 3. 数据安全
- 位置信息加密传输
- 用户隐私保护
- 数据访问权限控制

这个路线规划功能显著提升了送货签收的用户体验，通过智能化的路线优化帮助送货员提高配送效率，减少配送时间和成本。
