# queryParams 缓存问题修复说明

## 问题描述
`this.queryParams.class_id` 总是一个固定的值，而且是另一个租户的值，好像是缓存的，导致查询不到结果。

## 问题根本原因

### 1. 跨租户缓存问题
- **问题**: 应用使用 `localStorage` 存储查询条件，但没有区分不同租户
- **影响**: 当用户切换租户时，仍然使用上一个租户的 `class_id` 等参数
- **结果**: 查询时使用了错误租户的分类ID，导致查询不到结果

### 2. queryData 方法缺失
- **问题**: `GoodsArchives.vue` 中的 `queryData` 方法被注释掉了
- **影响**: `mounted` 和 `submitSelect` 中调用 `this.queryData()` 时方法不存在
- **结果**: `queryParams` 没有被正确传递给子组件

### 3. 缓存管理不当
- **问题**: 没有在租户切换时清空相关缓存
- **影响**: 新租户使用旧租户的查询参数
- **结果**: 数据混乱，查询结果不正确

## 解决方案

### 1. 添加租户检查机制
```javascript
// 在 mounted 钩子中添加租户检查
const currentOperKey = this.$store.state.operKey;
const cachedOperKey = localStorage.getItem('cached_operKey');

if (currentOperKey !== cachedOperKey) {
  // 如果租户发生变化，清空相关缓存
  console.log('检测到租户变化，清空缓存');
  localStorage.removeItem('class_id');
  localStorage.removeItem('class_name');
  localStorage.removeItem('brand_id');
  localStorage.removeItem('brand_name');
  localStorage.removeItem('status');
  localStorage.removeItem('sortFld1');
  localStorage.removeItem('sortFld2');
  localStorage.removeItem('sortFld3');
  localStorage.setItem('cached_operKey', currentOperKey);
}
```

### 2. 添加 updateQueryParams 方法
```javascript
updateQueryParams() {
  this.queryParams = {
    brand_id: this.formObj.brand_id || null,
    brand_name: this.formObj.brand_name || null,
    class_name: this.class_name || "0",
    class_id: this.class_id || "0",
    status: this.status === "" ? "all" : this.status,
    sortFld1: this.sortFld1,
    sortFld2: this.sortFld2,
    sortFld3: this.sortFld3
  };
  console.log("更新查询参数:", this.queryParams);
}
```

### 3. 修复方法调用
- **mounted**: 调用 `this.updateQueryParams()` 而不是不存在的 `this.queryData()`
- **submitSelect**: 调用 `this.updateQueryParams()` 更新查询参数
- **cancelSelect**: 调用 `this.updateQueryParams()` 重置查询参数

### 4. 改进缓存管理
- 在保存缓存时同时保存当前租户信息
- 在读取缓存时检查租户是否一致
- 租户不一致时自动清空相关缓存

## 修改的文件

### GoodsArchives.vue
- **mounted 方法**: 添加租户检查和缓存清理逻辑
- **updateQueryParams 方法**: 新增方法，统一管理查询参数更新
- **submitSelect 方法**: 修改为调用 `updateQueryParams`
- **cancelSelect 方法**: 修改为调用 `updateQueryParams`

## 修复效果

### 修复前
- `class_id` 总是固定值，可能是其他租户的
- 切换租户后仍使用旧租户的查询条件
- 查询结果不正确或为空

### 修复后
- 租户切换时自动清空缓存
- `class_id` 正确对应当前租户
- 查询参数正确传递给子组件
- 查询结果正确显示

## 数据流程

1. **组件挂载**:
   - 检查当前租户是否与缓存租户一致
   - 不一致则清空缓存
   - 从 localStorage 读取查询条件
   - 调用 `updateQueryParams` 更新查询参数

2. **查询参数传递**:
   - `GoodsArchives.vue` 的 `queryParams` 传递给 `ItemArchives.vue`
   - `ItemArchives.vue` 的 `queryParams` 传递给 `ItemDetail.vue`
   - `ItemDetail.vue` 使用 `queryParams` 进行数据查询

3. **参数更新**:
   - 用户修改查询条件时调用 `submitSelect`
   - `submitSelect` 保存条件到 localStorage 并更新 `queryParams`
   - Vue 的响应式系统自动触发子组件重新查询

## 注意事项

1. **租户标识**: 使用 `this.$store.state.operKey` 作为租户标识
2. **缓存键名**: 保持现有的缓存键名不变，确保兼容性
3. **默认值**: 确保所有查询参数都有合理的默认值
4. **错误处理**: 添加了 console.log 用于调试和问题排查

## 测试建议

1. **单租户测试**: 验证查询功能正常工作
2. **多租户测试**: 切换租户后验证缓存是否正确清空
3. **缓存测试**: 验证查询条件是否正确保存和恢复
4. **边界测试**: 测试空值、默认值等边界情况
