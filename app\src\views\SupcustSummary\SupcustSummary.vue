<template>
  <div class="pages" >
    <!-- 标题 -->
    <van-nav-bar
      title="客户汇总"
      left-arrow
      @click-left="myGoBack($router)"
      safe-area-inset-top
    >
    <!-- 右边按钮 -->
    <template #right>
      <div class="iconfont icon_right" @click="selectShow = !selectShow">
        &#xe690;
      </div>
    </template>
    </van-nav-bar>

    <van-tabs v-model="active" @change="tabChange">
      <van-tab title="今日" name="0" title-active-color="#1989fa">
      </van-tab>
      <van-tab  title="昨天" name="1" title-active-color="#1989fa">
      </van-tab>      
      <van-tab  title="本月" name="2" title-active-color="#1989fa">
      </van-tab>      
      <van-tab  title="日期" name="3" title-active-color="#1989fa">
      </van-tab>      

    </van-tabs>

    <van-field
          v-model="queryCondiLabels.dateTimeInfo"
          readonly
          placeholder="日期范围"
          @click="showDate = true" 
          label=" "
        />

    <van-popup
      class="van_popup"
      duration="0.4"
      v-model="selectShow"
      position="right"
      safe-area-inset-bottom
      :style="{ width: '85%', height: '100%' }"
    >
      <div style="height:30px;border-top:1px solid #ccc"></div>
      <van-field
            v-model="sellerName"
            readonly
            label="业务员"
            placeholder="请选择"
            label-width="70px"
            :disabled="disabledShowSeller"
            @click="showSellerClick()"
            style="border-bottom:2px solid #eee"
        >
        </van-field> 
      
      
      <div class="footer_button">

        <van-button @click="cancelSelect" :disabled="disabledShowSeller" >清空选择</van-button >
        <van-button @click="submitSelect"  plain type="info"  >确认选择</van-button
        >
      </div>
    </van-popup>
    <van-calendar
      v-model="showDate"
      type="range"
      @confirm="onConfirm"
      title="请选择起止日期"
      :allow-same-day="true"
      :min-date="minDate"
      :max-date="maxDate"
    />
    <van-popup
        v-model="showSeller"
        position="bottom"
        close-on-click-overlay
        close-on-popstate
        safe-area-inset-bottom
        duration="0.4"
        :style="{ width: '100%', height: '90%' }"
    >
    <SelectSeller @selectSellers="selectSeller"/>
    </van-popup>

    <div class="title3">
      <div>客户</div>
      <div>拜访次数</div>
      <div>销售额</div>
            <div>退货额</div>

     </div>

     <div class="title3">
      <div  >全部</div>
      <div  >{{ this.total_visit_count }}</div>
      <div  > {{ this.total_sale_amount }}</div>
      <div  > {{ this.total_t_amount }}</div>

     </div>




<div  class="public_box2_b"  v-if="records">

  <van-list
v-model="loading"
:finished="finished"
finished-text="到底了"
@load="PageQuery"
offset="10"
:immediate-check="false"
>
  <ul class="receive">
    <li v-for="(item, index) in records" :key="index" >
      <h4  @click="toVisitDetail(item.supcust_id,item.sup_name)">
      <div  >{{item.sup_name}}</div>
      <div >{{item.visit_count}}</div>
      <div >{{item.sum_sale_amount}}</div>
      <div >{{item.sum_t_amount}}</div>


      </h4>
    </li>
  </ul>
</van-list>

</div>
  
  




  </div>
</template>

<script>
import {
  Icon,NavBar,Popup,Calendar,CellGroup,Button,Field,Tab,Tabs,Cell,Form,List,Row
} from "vant";
import { GetSupcustSummary, GetVisitSummaryBySeller } from "../../api/api";
import SelectSeller from "../components/SelectSeller";

export default {
  name: "VisitSummaryBySeller",
  data() {
    return {       
      active: 1,
      selectShow: false,//展示右侧菜单
      minDate: new Date(2000, 1, 1),
      maxDate: new Date(),
      showDate: false,//弹出日期选择
      showSeller: false,
      disabledShowSeller:false,
      viewRange :"",
      sellerId :"",
      sellerName :"",    
       records: [],
      itemCount:0,
      total_sale_amount:0,
      total_visit_count:0,
      total_t_amount:0,
      finished:false,
      loading: false,
      isQuerying:false,
      pageSize: 20,
      startRow: 0,
      queryCondiValues: {
      startDate: "",
      endDate: "",
      },
      queryCondiLabels: {
        dateTimeInfo:'',
      }
    };
  },
  components: {
    "van-nav-bar": NavBar,
    "van-popup": Popup,
    "van-icon": Icon,
    "van-calendar": Calendar,
    "van-cell-group": CellGroup,
    "van-button": Button,
    "van-field": Field,
    "van-tabs": Tabs,
    "van-tab": Tab,
    "van-cell": Cell,
    "van-form": Form,
    "van-list": List,
    "van-row": Row,
    SelectSeller,


  },
  async mounted() { 
       this.viewRange = window.getRightValue("delicacy.sheetViewRange.value");
      if(this.viewRange=="self"){
        console.log(this.$store.state.operInfo);
        this.sellerId = this.$store.state.operInfo.oper_id;
         this.disabledShowSeller=true
      }

     this.handleDate(0,0);
     this.submitSelect();

  },
  methods: {
    
    computeDate(days) {
        var d = new Date();
        d.setDate(d.getDate() + days);
        var m = d.getMonth() + 1;
        return d.getFullYear() + '-' + m + '-' + d.getDate();
    },
    onConfirm(date) {
      const [start, end] = date;
      this.showDate = false;
      this.queryCondiValues.startDate = `${this.formatDate(start)}`;
      this.queryCondiValues.endDate = `${this.formatDate(end)}`;
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + " 至 " + this.queryCondiValues.endDate;
      this.submitSelect();     
     this.active="3"
    },
    handleDate(startDay,endDay) {
      this.queryCondiValues.startDate = this.computeDate(startDay)
      this.queryCondiValues.endDate = this.computeDate(endDay)
      this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + "至" + this.queryCondiValues.endDate;
    },

    //清空选择
    cancelSelect() {
      this.selectShow = false;
        this.clear();
    },
    //选择后执行刷新操作
    async submitSelect() {
      this.selectShow = false;

      this.newQuery();
    },
    
    newQuery(){
      this.startRow = 0;
      this.finished = false;
      this.itemCount=0;
      this.records = [];
      this.PageQuery();
    },
    
    PageQuery() {
     
      if (this.finished) return;
      this.loading = !this.loading;
      let params = {
        ...this.queryCondiValues,
        pageSize: this.pageSize,
        startRow: this.startRow,
        startDate:this.queryCondiValues.startDate?this.queryCondiValues.startDate:null,
        endDate:this.queryCondiValues.endDate?this.queryCondiValues.endDate:null,
        sellerId:this.sellerId,
      }
      if(this.isQuerying) 
        return  //有时候在清空列表的时候会自动触发load事件，所以在查询完毕后才可以进行下一次查询
      else
        this.isQuerying=true

      GetSupcustSummary(params).then((res)=>{
        if (res.result === "OK") {

          if(this.startRow ==0){

          this.itemCount=res.itemCount

          this.total_sale_amount=res.total.sum_sale_amount
          this.total_visit_count=res.total.visit_count
          this.total_t_amount=res.total.sum_t_amount
          }
          this.records.push(...res.data) 

          this.loading = false

        this.startRow = Number(this.startRow)+this.pageSize
          if(this.records.length >= Number(this.itemCount)){
            this.finished = true
          }


         this.isQuerying=false
        }

        })


    },
  clear(){
      this.isQuerying=false
      this.finished=false
      this.records=[]
      this.itemCount=0
      this.startRow= 0
      this.sellerId=""
      this.sellerName=""
    },
    async tabChange(key) { 
   switch(key){

    case "0":
    this.handleDate(0,0);
    this.submitSelect();

    break;

    case "1":
    this.handleDate(-1,-1);
    this.submitSelect();

    break;
    case "2":
    var d = new Date();
    var m = d.getMonth() + 1;
    this.queryCondiValues.startDate = d.getFullYear() + '-' + m+ '-' + '01';
    this.queryCondiValues.endDate = d.getFullYear() + '-' + m + '-' + d.getDate();
    this.queryCondiLabels.dateTimeInfo = this.queryCondiValues.startDate + "至" + this.queryCondiValues.endDate;
    this.submitSelect();
    break;
   }

    },
    toVisitDetail(supcust_id,supcust_name) {
      this.$router.push({
        path: "/VisitRecord",
        query: {
          supcustID:supcust_id, 
          supcustName:supcust_name,
          startDate:this.queryCondiValues.startDate ,
          endDate: this.queryCondiValues.endDate,
          submitSelect:1
        },

        
      });
    },
    selectSeller(value) {
      this.sellerId= value.oper_id;
      this.sellerName  = value.oper_name;
      this.showSeller = false


    },
    showSellerClick(){
      this.showSeller = !this.disabledShowSeller;
    }
    
},
};
</script>

<style lang="less" scoped>
@flex_acent_jbw: {
  display: flex;
  align-items: center;
  justify-content: space-between;
};
@flex_acent_jend: {
  display: flex;
  align-items: center;
  justify-content: flex-end;
};
@flex_flex_jc: {
  display: flex;
  align-items: center;
  justify-content: center;
};
.report_no_box{
  width: 100%;
  height: calc(100% - 46px);
  @flex_flex_jc();
  background: #f2f2f2;
  color: #999999;
  .whole_box_no_icon{
    font-size: 50px;
  }
  p{
    font-size: 14px;
  }
}
/deep/.van-cell{
  font-size: 15px;
}
.public_box2_b{
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

}
.van_popup{
  .select_list{
    height: 46px;
    background: #f2f2f2;
    position: relative;
    h4{
      height: 100%;
      @flex_flex_jc();
      font-size: 16px;
    }
    button {
      width: 50px;
      height: 30px;
      position: absolute;
      right: 10px;
      top: 0;
      bottom: 0;
      font-size: 15px;
      margin-top: 8px;
      color: #000000;
    }
  }
  .select_filter{
    height: calc(100% - 46px - 50px);
    overflow-x: hidden;
    overflow-y: auto;
  }
  .select_button{
    width: 100%;
    height: 40px;
    vertical-align: top;
    margin-bottom: 10px;
    button{
      height: 100%;
      vertical-align: top;
    }
  }
}
.receive {
   height:100%;
   overflow:auto;
  padding: 5px;
  background: #ffffff;
  height: 100%;  
  li {
    min-height: 50px;
    border-top: 1px solid #f2f2f2;
    border-bottom: 1px solid #f2f2f2;
    h4 {
      min-height: 50px;
      width: 100%;
      @flex_acent_jbw();
      font-weight: normal;
      div {
        width: calc(33.33% - 10px);
        font-size: 15px;
        padding: 0 5px;
        text-align: center;
        color: #333333;
      }
      div:first-child {
        text-align: left;
      }
      div:last-child {
        text-align: right;
        @flex_acent_jend();
        i {
          font-size: 15px;
          margin-left: 10px;
        }
      }
    }
    .receive_son {
      height: auto;
      overflow: hidden;
      padding: 0 10px;
      background: #dddddd;
      .receive_son_item {
        height: auto;
        overflow: hidden;
        padding: 8px 0;
        border-bottom: 1px solid #f5f5f5;
        @flex_acent_jbw();
        div {
          width: calc(33.33% - 10px);
          font-size: 13px;
          padding: 0 5px;
          text-align: center;
          color: #666666;
        }
        div:first-child {
          text-align: left;
        }
        div:last-child {
          text-align: right;
          @flex_acent_jend();
          i {
            font-size: 15px;
            margin-left: 10px;
          }
        }
      }
      .receive_son_item:last-child{
        border: none
      }
    }
  }
  li:last-child {
      border-bottom: none;
    }
  .receive_li_open{
    height: auto;
    i{
      transition: all 0.3s;
      transform: rotate(90deg);
    }
  }
}
.receive_title {
  span {
    display: block;
  }
  span:last-child {
    margin-top: 5px;
  }
}
.advance_footer{
  width: 100%;
  height: 50px;
  overflow: hidden;
  font-size: 15px;
  @flex_acent_jbw();
  padding: 0 5px;
  box-sizing: border-box;
  background: #fff;
  border-top: 1px solid #f2f2f2;
   box-shadow:0 -2px 5px #F2F6FC;
  .advance_footer_center{
    span{
      display: block;
    }
  }
}
.handle_date_btns{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-top: 10px;
  .date_btn{
    margin-bottom: 20px;
    flex: 50%;

    button {
      width: 60%;
      background-color: #f4f4f4;
      border: none;
      border-radius: 5px;
    }
  }
}
.footer_button {
  width: 100%;
  height: 45px;
  margin-top: 10px;
  vertical-align: top;
  button {
    width: 100px;
    height: inherit;
    margin: 0 20px;
    vertical-align: top;
  }
}
.buttom{
  display: flex;
  align-items: center;
  justify-content: space-between;

}
.select_button{
  height: 45px;
  border-radius:12px;
  background-color:#fff;
  border:1px solid #ccc" plain type="default
}


</style>