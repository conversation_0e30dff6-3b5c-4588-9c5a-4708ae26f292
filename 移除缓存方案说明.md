# 移除缓存方案说明

## 方案概述
完全移除 localStorage 缓存机制，每次组件挂载时都使用默认值，避免跨租户缓存污染问题。

## 为什么选择不使用缓存？

### 1. 避免跨租户污染
- **问题**: localStorage 是全局的，不区分租户
- **风险**: 租户A的查询条件会影响租户B
- **解决**: 不使用缓存，每次都是干净的状态

### 2. 简化逻辑
- **问题**: 缓存管理增加了复杂性
- **风险**: 需要处理缓存失效、清理等逻辑
- **解决**: 移除缓存，逻辑更简单可靠

### 3. 数据一致性
- **问题**: 缓存可能与实际状态不一致
- **风险**: 用户看到的不是最新状态
- **解决**: 每次都使用最新的默认值

## 修改内容

### 1. GoodsArchives.vue

#### 1.1 mounted 方法
```javascript
// 修改前：从 localStorage 读取缓存
this.class_id = localStorage.getItem('class_id') || '';
this.class_name = localStorage.getItem('class_name') || '';
// ...

// 修改后：使用默认值
this.initializeDefaultValues();
```

#### 1.2 新增 initializeDefaultValues 方法
```javascript
initializeDefaultValues() {
  console.log('初始化默认值');
  this.class_id = '';
  this.class_name = '';
  this.formObj.brand_id = '';
  this.formObj.brand_name = '';
  this.status = '1';
  this.sortFld1 = 'order_index';
  this.sortFld2 = 'item_name';
  this.sortFld3 = 'none';
  console.log('默认值初始化完成');
}
```

#### 1.3 submitSelect 方法
```javascript
// 修改前：保存到 localStorage
localStorage.setItem('class_id', this.class_id || '');
localStorage.setItem('class_name', this.class_name || '');
// ...

// 修改后：只记录日志，不保存缓存
console.log('提交查询条件:', {
  class_id: this.class_id,
  class_name: this.class_name,
  // ...
});
```

#### 1.4 cancelSelect 方法
```javascript
// 修改前：清空 localStorage
localStorage.removeItem('class_id');
localStorage.removeItem('class_name');
// ...

// 修改后：重置为默认值
this.initializeDefaultValues();
```

#### 1.5 handleShowSunitPrice 方法
```javascript
// 修改前：保存到 localStorage
localStorage.setItem('sUnitPriceShow', this.sUnitPriceCheck)

// 修改后：不保存缓存
// 不使用缓存，每次都重新设置
```

### 2. ItemDetail.vue

#### 2.1 mounted 方法
```javascript
// 修改前：从 localStorage 读取
this.sUnitPriceShow = localStorage.getItem('sUnitPriceShow')=='true'?true:false

// 修改后：使用默认值
this.sUnitPriceShow = false;
```

## 默认值设计

### 查询条件默认值
- `class_id`: `''` (空字符串，表示不限制分类)
- `class_name`: `''` (空字符串)
- `brand_id`: `''` (空字符串，表示不限制品牌)
- `brand_name`: `''` (空字符串)
- `status`: `'1'` (默认查询启用状态的商品)
- `sortFld1`: `'order_index'` (按排序索引排序)
- `sortFld2`: `'item_name'` (按商品名称排序)
- `sortFld3`: `'none'` (无第三排序字段)

### 显示选项默认值
- `sUnitPriceShow`: `false` (默认不显示小单位价格)

## 用户体验影响

### 正面影响
1. **数据准确性**: 每次都是最新、正确的数据
2. **租户隔离**: 不会出现跨租户数据混乱
3. **简单可靠**: 减少了缓存相关的bug

### 可能的负面影响
1. **用户设置丢失**: 用户的筛选条件不会保存
2. **重复操作**: 每次进入页面都需要重新设置条件

### 缓解措施
1. **合理的默认值**: 选择最常用的默认设置
2. **快速筛选**: 提供常用筛选条件的快捷按钮
3. **会话内保持**: 在同一个会话中，用户设置的条件会保持

## 数据流程

### 1. 组件挂载流程
```
组件挂载 → initializeDefaultValues() → queryData('mounted') → 查询数据
```

### 2. 用户筛选流程
```
用户设置条件 → submitSelect() → queryData('submitSelect') → 查询数据
```

### 3. 重置筛选流程
```
用户点击重置 → cancelSelect() → initializeDefaultValues() → queryData('cancelSelect') → 查询数据
```

## 技术优势

### 1. 代码简化
- 移除了大量 localStorage 相关代码
- 减少了租户检查逻辑
- 简化了错误处理

### 2. 维护性提升
- 减少了状态管理的复杂性
- 降低了bug出现的可能性
- 更容易理解和调试

### 3. 性能优化
- 减少了 localStorage 的读写操作
- 避免了缓存检查的开销
- 启动速度更快

## 测试要点

### 1. 功能测试
- 验证默认查询条件是否正确
- 验证筛选功能是否正常工作
- 验证重置功能是否正确

### 2. 租户隔离测试
- 切换租户后验证查询条件是否重置
- 验证不同租户之间没有数据干扰

### 3. 用户体验测试
- 验证页面加载速度
- 验证操作流畅性
- 收集用户对默认值的反馈

## 后续优化建议

### 1. 会话级缓存
如果需要在会话内保持用户设置，可以考虑：
- 使用 Vuex 存储当前会话的设置
- 在页面刷新时重置为默认值

### 2. 用户偏好设置
如果需要长期保存用户偏好，可以考虑：
- 将设置保存到服务器端
- 与用户账号关联，支持跨设备同步

### 3. 智能默认值
根据用户使用习惯动态调整默认值：
- 分析用户最常用的筛选条件
- 提供个性化的默认设置
